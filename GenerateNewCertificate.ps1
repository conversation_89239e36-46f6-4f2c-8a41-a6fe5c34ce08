# Create a directory for the certificate if it doesn't exist
$certDir = "$PSScriptRoot\Certs"
if (-not (Test-Path -Path $certDir)) {
    New-Item -ItemType Directory -Path $certDir | Out-Null
}

# Path to save the certificate
$certPath = "$certDir\UnitedPrint_New_Certificate.pfx"

# Generate a secure password for the certificate
$password = ConvertTo-SecureString -String "YourSecurePassword123!" -Force -AsPlainText

# Create a new self-signed certificate
$cert = New-SelfSignedCertificate \
    -Type Custom \
    -Subject "CN=UnitedPrint New Certificate, O=UnitedPrint, C=US" \
    -KeyUsage DigitalSignature \
    -FriendlyName "UnitedPrint New Certificate" \
    -CertStoreLocation "Cert:\CurrentUser\My" \
    -KeyExportPolicy Exportable \
    -KeySpec Signature \
    -KeyLength 2048 \
    -KeyAlgorithm RSA \
    -TextExtension @("*********={text}*******.*******.3", "*********={text}")

# Export the certificate to a file
Export-PfxCertificate \
    -Cert $cert \
    -FilePath $certPath \
    -Password $password \
    -NoProperties | Out-Null

# Install the certificate to Trusted Root and Trusted Publishers stores
Write-Host "Installing certificate to Trusted Root and Trusted Publishers stores..." -ForegroundColor Yellow
Write-Host "This requires administrator privileges. You may be prompted for confirmation." -ForegroundColor Yellow

# Import to Trusted Root
$rootStore = New-Object System.Security.Cryptography.X509Certificates.X509Store -ArgumentList "Root", "LocalMachine"
$rootStore.Open("ReadWrite")
$rootStore.Add($cert)
$rootStore.Close()

# Import to Trusted Publishers
$publisherStore = New-Object System.Security.Cryptography.X509Certificates.X509Store -ArgumentList "TrustedPublisher", "LocalMachine"
$publisherStore.Open("ReadWrite")
$publisherStore.Add($cert)
$publisherStore.Close()

Write-Host "`nCertificate has been created and installed successfully!" -ForegroundColor Green
Write-Host "Certificate path: $certPath" -ForegroundColor Green
Write-Host "Certificate thumbprint: $($cert.Thumbprint)" -ForegroundColor Green
Write-Host "`nPlease note the thumbprint above. You'll need it to update your project file." -ForegroundColor Yellow
Write-Host "Run this script as Administrator to ensure the certificate is properly installed in the trusted stores." -ForegroundColor Yellow
