<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">net10.0-windows10.0.19041.0</TargetFrameworks>
    <OutputType>Exe</OutputType>
    <RootNamespace>unitedprint</RootNamespace>
    <UseMaui>true</UseMaui>
    <SingleProject>true</SingleProject>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <!-- Application Info -->
    <ApplicationTitle>UnitedPrint</ApplicationTitle>
    <ApplicationId>com.unitededucation.unitedprint</ApplicationId>
    <ApplicationDisplayVersion>1.0.0</ApplicationDisplayVersion>
    <ApplicationVersion>1</ApplicationVersion>

    <!-- Windows specific settings -->
    <WindowsTargetPlatformMinVersion>10.0.19041.0</WindowsTargetPlatformMinVersion>
    <WindowsTargetPlatformVersion>10.0.19041.0</WindowsTargetPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</SupportedOSPlatformVersion>
    <TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</TargetPlatformMinVersion>

    <!-- MSIX Package Settings -->
    <WindowsPackageType>MSIX</WindowsPackageType>
    <WindowsAppSDKSelfContained>true</WindowsAppSDKSelfContained>
    <GenerateAppxPackageOnBuild>true</GenerateAppxPackageOnBuild>
    <AppxBundle>Always</AppxBundle>
    <AppxBundlePlatforms>x64</AppxBundlePlatforms>
    <AppxAutoIncrementPackageRevision>True</AppxAutoIncrementPackageRevision>
    <AppxManifest>Platforms\Windows\Package.appxmanifest</AppxManifest>

    <!-- Package Signing with Certificate -->
    <AppxPackageSigningEnabled>False</AppxPackageSigningEnabled>
    <AppxPackageSigningDigestAlgorithm>SHA256</AppxPackageSigningDigestAlgorithm>
    <AppxPackageSigningTimestampDigestAlgorithm>SHA256</AppxPackageSigningTimestampDigestAlgorithm>
    <AppxPackageSigningTimestampUrl>http://timestamp.digicert.com</AppxPackageSigningTimestampUrl>

    <!-- App Installer Settings -->
    <GenerateAppInstallerFile>True</GenerateAppInstallerFile>
    <AppInstallerCheckForUpdateFrequency>OnApplicationRun</AppInstallerCheckForUpdateFrequency>
    <AppInstallerUpdateFrequency>0</AppInstallerUpdateFrequency>
    <AppxPackageDir>bin\Release\AppPackages\</AppxPackageDir>
    <AppInstallerUri>C:\Users\<USER>\Desktop\exe</AppInstallerUri>
    <HoursBetweenUpdateChecks>0</HoursBetweenUpdateChecks>

    <!-- Additional Settings -->
    <AppxSymbolPackageEnabled>False</AppxSymbolPackageEnabled>
    <GenerateTestArtifacts>True</GenerateTestArtifacts>
  </PropertyGroup>

	<ItemGroup>
		<!-- App Icon -->
		<MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#512BD4" />

		<!-- Splash Screen -->
		<MauiSplashScreen Include="Resources\Images\search_logo.png" Color="#FFFFFF" BaseSize="200,80" />

		<!-- Images -->
		<MauiImage Include="Resources\Images\dotnet_bot.png" />
		<MauiImage Include="Resources\Images\edis_logo.png" />
		<MauiImage Include="Resources\Images\edis_small_logo.png" />
		<MauiImage Include="Resources\Images\eia_logo.png" />
		<MauiImage Include="Resources\Images\search_logo.png" />
		<MauiImage Include="Resources\Images\image_one.png" />
		<MauiImage Include="Resources\Images\image_four.png" />
		<MauiImage Include="Resources\Images\image_five.png" />
		<MauiImage Include="Resources\Images\image_fivehundred.jpg" />
		<MauiImage Include="Resources\Images\logo_two.png" />
		<MauiImage Include="Resources\Images\logo_three.png" />
		<MauiImage Include="Resources\Images\eia_logo_copy.png" />

		<MauiImage Update="Resources\Images\dotnet_bot.png" Resize="True" BaseSize="300,185" />
		<MauiImage Update="Resources\Images\edis_logo.png" Resize="True" BaseSize="300,120" />
		<MauiImage Update="Resources\Images\edis_small_logo.png" Resize="True" BaseSize="60,60" />
		<MauiImage Update="Resources\Images\eia_logo.png" Resize="True" BaseSize="60,60" />
		<MauiImage Update="Resources\Images\search_logo.png" Resize="True" BaseSize="200,80" />

		<!-- Custom Fonts -->
		<MauiFont Include="Resources\Fonts\*" />

		<!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
		<MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.Maui.Controls" Version="9.0.10" />
		<PackageReference Include="Microsoft.Maui.Controls.Compatibility" Version="9.0.10" />
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.0" />
		<PackageReference Include="System.Net.Http" Version="4.3.4" />
		<PackageReference Include="System.Text.Json" Version="9.0.0" />
		<PackageReference Include="ZXing.Net" Version="0.16.9" />
		<PackageReference Include="ZXing.Net.Bindings.SkiaSharp" Version="0.16.9" />
		<PackageReference Include="Microsoft.Maui.Graphics.Skia" Version="9.0.10" />
	</ItemGroup>

	<ItemGroup Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">
		<PackageReference Include="System.Drawing.Common" Version="9.0.0" />
	</ItemGroup>

</Project>

