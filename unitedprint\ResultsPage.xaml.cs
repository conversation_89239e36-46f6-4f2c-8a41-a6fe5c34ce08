using Microsoft.Maui.Controls;
using System.Text.Json;
using unitedprint.Services;

namespace unitedprint
{
    public partial class ResultsPage : ContentPage
    {
        private JsonElement _registrationData;
        private string _phoneNumber;
        private readonly PrintingService _printingService;

        public ResultsPage(PrintingService printingService)
        {
            InitializeComponent();
            _printingService = printingService;
        }

        public ResultsPage(JsonElement registrationData, string phoneNumber, PrintingService printingService = null) 
            : this(printingService ?? new PrintingService())
        {
            _registrationData = registrationData;
            _phoneNumber = phoneNumber;
            LoadRegistrationData();
        }

        private void LoadRegistrationData()
        {
            try
            {
                // Extract student name from the API response (using underscore format)
                string studentName = "Unknown Student";
                if (_registrationData.TryGetProperty("student_name", out var nameElement))
                {
                    studentName = nameElement.GetString() ?? "Unknown Student";
                }

                // Extract student phone from the API response
                string studentPhone = _phoneNumber;
                if (_registrationData.TryGetProperty("student_phone", out var phoneElement))
                {
                    studentPhone = phoneElement.GetString() ?? _phoneNumber;
                }

                // Set the labels - only name and phone
                StudentNameLabel.Text = studentName;
                PhoneNumberLabel.Text = studentPhone;
            }
            catch (Exception ex)
            {
                // Fallback in case of any issues
                StudentNameLabel.Text = "Registration Found";
                PhoneNumberLabel.Text = _phoneNumber;
            }
        }

        private async void OnAttendeeNumberClicked(object sender, EventArgs e)
        {
            if (sender is Button button)
            {
                int attendeeCount = int.Parse(button.Text);
                
                // Show confirmation
                bool confirm = await DisplayAlert(
                    "Confirm Selection", 
                    $"You selected {attendeeCount} attendee(s) for {StudentNameLabel.Text}.\n\nProceed with printing?", 
                    "Yes, Print", 
                    "Cancel"
                );

                if (confirm)
                {
                    // Here you can add printing logic or navigate to a printing page
                    await ProcessPrintRequest(attendeeCount);
                }
            }
        }

        private async Task ProcessPrintRequest(int attendeeCount)
        {
            try
            {
                // Extract registration ID for tracking
                string registrationId = "";
                if (_registrationData.TryGetProperty("id", out var idElement))
                {
                    registrationId = idElement.GetInt32().ToString();
                }

                // Execute the printing functionality directly without processing dialog
                string printResult = await _printingService.PrintBadges(
                    registrationId,
                    StudentNameLabel.Text,
                    attendeeCount,"AR"
                );

                // Show result message
                if (printResult.Contains("successfully"))
                {
                    var successMessage = $"Print request completed successfully!\n\n" +
                                        $"Attendees: {attendeeCount}\n" +
                                        $"Student: {StudentNameLabel.Text}\n" +
                                        $"Phone: {PhoneNumberLabel.Text}";

                    if (!string.IsNullOrEmpty(registrationId))
                        successMessage += $"\nRegistration ID: {registrationId}";

                    await DisplayAlert("Success", successMessage, "OK");
                }
                else
                {
                    // Show error message from printing service
                    await DisplayAlert("Printing Error", printResult, "OK");
                }

                // Navigate back to main page
                await Shell.Current.GoToAsync("..");
            }
            catch (Exception ex)
            {
                await DisplayAlert("Error", $"Print request failed: {ex.Message}", "OK");
            }
        }

        private async void OnBackToHomeClicked(object sender, EventArgs e)
        {
            // Navigate back to the main page
            await Shell.Current.GoToAsync("..");
        }
    }
}
