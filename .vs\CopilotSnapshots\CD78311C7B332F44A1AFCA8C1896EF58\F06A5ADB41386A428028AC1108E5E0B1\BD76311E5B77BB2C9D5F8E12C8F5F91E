﻿using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Maui.Graphics;
using Microsoft.Maui.Graphics.Skia;
using ZXing;
using ZXing.Common;
using ZXing.QrCode;
using SkiaSharp;
using ZXing.SkiaSharp;
using ZXing.SkiaSharp.Rendering;

#if WINDOWS
using System.Drawing;
using System.Drawing.Imaging;
using System.Drawing.Printing;
using System.Text;
using System.Net.Sockets;
using System.Runtime.InteropServices;
using Microsoft.Win32;
using System.Collections.Generic;
using System.Linq;
#endif

namespace unitedprint.Services
{
    public class PrintingService
    {
        public async Task<string> PrintBadges(string studentId, string studentName, int attendeeCount)
        {
            try
            {
#if WINDOWS
                return await PrintBadgesWindows(studentId, studentName, attendeeCount);
#else
                return await Task.FromResult("Printing is only supported on Windows platform.");
#endif
            }
            catch (Exception ex)
            {
                return $"Printing failed: {ex.Message}";
            }
        }

#if WINDOWS
        // Define enum for Zebra printer connection types
        private enum ZebraPrinterConnectionType
        {
            Unknown,
            USB,
            Network
        }
        
        // Define a struct to hold Zebra printer information
        private struct ZebraPrinterInfo
        {
            public bool found;
            public string name;
            public string address;
            public ZebraPrinterConnectionType connectionType;
            
            public ZebraPrinterInfo(bool found, string name, string address, ZebraPrinterConnectionType connectionType)
            {
                this.found = found;
                this.name = name;
                this.address = address;
                this.connectionType = connectionType;
            }
        }

        private async Task<string> PrintBadgesWindows(string studentId, string studentName, int attendeeCount)
        {
            try
            {
                // First try to find a Zebra printer
                var printerInfo = await Task.Run(() => FindZebraPrinter());
                
                if (printerInfo.found)
                {
                    // Try printing using Zebra printer
                    return await PrintToZebraPrinter(printerInfo, studentId, studentName, attendeeCount);
                }
                else
                {
                    // Fallback: Create badge images and save them to a folder
                    string outputFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "PrintedBadges");
                    if (!Directory.Exists(outputFolder))
                    {
                        Directory.CreateDirectory(outputFolder);
                    }

                    // Generate QR code data
                    string qrData = studentId;

                    // Create badges
                    for (int i = 1; i <= attendeeCount; i++)
                    {
                        string badgePath = await CreateBadgeImage(qrData, studentName, i, attendeeCount);

                        // Move the badge to the output folder instead of printing
                        string finalPath = Path.Combine(outputFolder, $"Badge_{studentName}_{i}_{DateTime.Now:yyyyMMdd_HHmmss}.png");
                        File.Move(badgePath, finalPath);
                    }

                    return $"No Zebra printer found. Badge images created successfully!\nSaved to: {outputFolder}";
                }
            }
            catch (Exception ex)
            {
                return $"Badge creation/printing error: {ex.Message}";
            }
        }
        
        private async Task<string> PrintToZebraPrinter(ZebraPrinterInfo printerInfo, string studentId, string studentName, int attendeeCount)
        {
            try
            {
                // Create a results list to track printing status for each badge
                var results = new List<string>();
                
                // Create and print badges
                for (int i = 1; i <= attendeeCount; i++)
                {
                    string badgePath = await CreateBadgeImage(studentId, studentName, i, attendeeCount);
                    
                    // Create ZPL command to print the image
                    byte[] zplCommand;
                    
                    if (printerInfo.connectionType == ZebraPrinterConnectionType.Network)
                    {
                        // For network printers, we'll send the image directly using ZPL
                        zplCommand = CreateZPLForImage(badgePath, studentName, i == 1);
                        var networkResult = SendZPLToNetworkPrinter(printerInfo.address, zplCommand);
                        results.Add($"Badge {i}: {networkResult}");
                    }
                    else if (printerInfo.connectionType == ZebraPrinterConnectionType.USB)
                    {
                        // For USB printers, we'll use the Windows printing system
                        var usbResult = PrintImageToUsbPrinter(badgePath, printerInfo.name);
                        results.Add($"Badge {i}: {usbResult}");
                    }
                    
                    // Clean up temporary file
                    if (File.Exists(badgePath))
                    {
                        File.Delete(badgePath);
                    }
                }
                
                // Determine overall result
                bool allSuccessful = results.All(r => r.Contains("success"));
                string statusMessage = allSuccessful ? 
                    $"All {attendeeCount} badges printed successfully to {printerInfo.name}" : 
                    $"Some badges may not have printed correctly: {string.Join(", ", results)}";
                
                return statusMessage;
            }
            catch (Exception ex)
            {
                return $"Error printing to Zebra printer: {ex.Message}";
            }
        }
        
        private ZebraPrinterInfo FindZebraPrinter()
        {
            try
            {
                // First check local USB printers
                foreach (string printerName in PrinterSettings.InstalledPrinters)
                {
                    PrinterSettings settings = new PrinterSettings();
                    settings.PrinterName = printerName;
                    
                    // Check if this is a Zebra printer by name
                    if (settings.IsValid && printerName.ToLower().Contains("zebra"))
                    {
                        return new ZebraPrinterInfo(true, printerName, "", ZebraPrinterConnectionType.USB);
                    }
                }
                
                // If no USB Zebra printer found, try to find network Zebra printers
                // by looking for printers with port names starting with "IP_"
                foreach (string printerName in PrinterSettings.InstalledPrinters)
                {
                    try
                    {
                        // Attempt to extract printer port information using the registry
                        string subKey = $@"SYSTEM\CurrentControlSet\Control\Print\Printers\{printerName}\DsSpooler";
                        using (var key = Registry.LocalMachine.OpenSubKey(subKey))
                        {
                            if (key != null)
                            {
                                // Get port names from registry
                                var portNameValue = key.GetValue("portName");
                                if (portNameValue != null)
                                {
                                    string portName = portNameValue.ToString();
                                    if (portName.StartsWith("IP_") || portName.StartsWith("TCP:"))
                                    {
                                        // Extract IP address from port name
                                        string ipAddress = portName.Replace("IP_", "").Replace("TCP:", "");
                                        
                                        if (printerName.ToLower().Contains("zebra"))
                                        {
                                            return new ZebraPrinterInfo(true, printerName, ipAddress, ZebraPrinterConnectionType.Network);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    catch
                    {
                        // Continue if we can't read registry information for this printer
                        continue;
                    }
                }
                
                // No Zebra printer found
                return new ZebraPrinterInfo(false, "", "", ZebraPrinterConnectionType.Unknown);
            }
            catch
            {
                // If anything goes wrong, return not found
                return new ZebraPrinterInfo(false, "", "", ZebraPrinterConnectionType.Unknown);
            }
        }
        
        private string PrintImageToUsbPrinter(string imagePath, string printerName)
        {
            try
            {
                using (var printDoc = new PrintDocument())
                {
                    printDoc.PrinterSettings.PrinterName = printerName;
                    
                    if (!printDoc.PrinterSettings.IsValid)
                    {
                        return $"Invalid printer: {printerName}";
                    }
                    
                    // Load the image to print
                    using Bitmap badgeImage = new Bitmap(imagePath);
                    
                    // Setup print handler
                    printDoc.PrintPage += (sender, e) =>
                    {
                        // Calculate scaling to fit the page while maintaining aspect ratio
                        Rectangle pageRect = e.PageBounds;
                        float scale = Math.Min(
                            (float)pageRect.Width / badgeImage.Width,
                            (float)pageRect.Height / badgeImage.Height);
                        
                        int scaledWidth = (int)(badgeImage.Width * scale);
                        int scaledHeight = (int)(badgeImage.Height * scale);
                        
                        // Center the image on the page
                        int x = (pageRect.Width - scaledWidth) / 2;
                        int y = (pageRect.Height - scaledHeight) / 2;
                        
                        // Draw the image
                        e.Graphics.DrawImage(badgeImage, x, y, scaledWidth, scaledHeight);
                    };
                    
                    // Print the document
                    printDoc.Print();
                    return "printed successfully";
                }
            }
            catch (Exception ex)
            {
                return $"printing error: {ex.Message}";
            }
        }
        
        private byte[] CreateZPLForImage(string imagePath, string studentName, bool isPrimaryBadge)
        {
            try
            {
                // Convert image to monochrome (1-bit) bitmap
                using Bitmap originalImage = new Bitmap(imagePath);
                using Bitmap monoImage = new Bitmap(originalImage.Width, originalImage.Height, System.Drawing.Imaging.PixelFormat.Format1bppIndexed);
                
                using (Graphics g = Graphics.FromImage(monoImage))
                {
                    g.DrawImage(originalImage, 0, 0);
                }
                
                // Save to memory stream as temporary BMP file
                using var ms = new MemoryStream();
                monoImage.Save(ms, System.Drawing.Imaging.ImageFormat.Bmp);
                byte[] imageBytes = ms.ToArray();
                
                // Create a hexadecimal string of the image data
                StringBuilder hexString = new StringBuilder();
                for (int i = 0; i < imageBytes.Length; i++)
                {
                    hexString.Append(imageBytes[i].ToString("X2"));
                }
                
                // Build ZPL command
                // We're creating a simple ZPL command to print a label with text and image
                StringBuilder zplCommand = new StringBuilder();
                
                // Start ZPL command
                zplCommand.Append("^XA");
                
                // Set label dimensions and orientation
                zplCommand.Append("^PW480^LL400");
                
                // Add header text
                zplCommand.Append("^FO160,20^A0N,28,28^FDEDIS Fair 2024^FS");
                
                // Add image (QR code and badge layout)
                zplCommand.Append("^FO50,60^GFA,8192,8192,80,");
                zplCommand.Append(hexString);
                zplCommand.Append("^FS");
                
                // Add name text
                string displayName = isPrimaryBadge ? studentName : "Visitor";
                zplCommand.Append($"^FO160,350^A0N,28,28^FD{displayName}^FS");
                
                // End ZPL command
                zplCommand.Append("^XZ");
                
                // Convert string to bytes
                return Encoding.ASCII.GetBytes(zplCommand.ToString());
            }
            catch (Exception ex)
            {
                // Return a simple error message ZPL
                string errorZpl = $"^XA^FO50,50^A0N,50,50^FDError: {ex.Message}^FS^XZ";
                return Encoding.ASCII.GetBytes(errorZpl);
            }
        }
        
        private string SendZPLToNetworkPrinter(string ipAddress, byte[] zplData)
        {
            try
            {
                // Default Zebra printer port
                int port = 9100;
                
                using (var client = new TcpClient())
                {
                    // Set a reasonable timeout
                    var connectTask = client.ConnectAsync(ipAddress, port);
                    if (!connectTask.Wait(5000)) // 5 second timeout
                    {
                        return $"Connection timeout to {ipAddress}:{port}";
                    }
                    
                    // Send the ZPL data
                    using (var stream = client.GetStream())
                    {
                        stream.Write(zplData, 0, zplData.Length);
                        stream.Flush();
                        
                        // Give a brief pause to ensure data is sent
                        Thread.Sleep(500);
                    }
                }
                
                return "sent successfully";
            }
            catch (Exception ex)
            {
                return $"network error: {ex.Message}";
            }
        }

        private async Task<string> CreateBadgeImage(string qrData, string studentName, int badgeNumber, int totalBadges)
        {
            const int badgeWidth = 480;
            const int badgeHeight = 400;

            // Create QR code
            var skQrCodeImage = GenerateQRCode(qrData);

            // Convert SKBitmap to System.Drawing.Bitmap
            System.Drawing.Bitmap qrCodeImage;
            using (var image = SKImage.FromBitmap(skQrCodeImage))
            using (var data = image.Encode(SKEncodedImageFormat.Png, 100))
            using (var stream = new MemoryStream(data.ToArray()))
            {
                qrCodeImage = new System.Drawing.Bitmap(stream);
            }
            
            // Create badge image
            using var badgeImage = new Bitmap(badgeWidth, badgeHeight);
            using var graphics = Graphics.FromImage(badgeImage);
            
            // Clear background
            graphics.Clear(System.Drawing.Color.White);

            // Draw title/header
            var topFont = new System.Drawing.Font("Arial", 22, System.Drawing.FontStyle.Bold);
            string headerText = "EDIS Fair 2024"; // You can customize this
            var topTextSize = graphics.MeasureString(headerText, topFont);
            var topTextLocation = new System.Drawing.PointF((badgeWidth - topTextSize.Width) / 2, 15);
            graphics.DrawString(headerText, topFont, System.Drawing.Brushes.Black, topTextLocation);

            // Draw QR code
            const int qrSize = 280;
            int qrX = (badgeWidth / 2) - (qrSize / 2) - 25;
            int qrY = 60;
            graphics.DrawImage(qrCodeImage, qrX, qrY, qrSize, qrSize);

            // Draw language circle (you can customize the language code)
            string languageCode = "EN"; // Default to English, you can make this configurable
            DrawLanguageCircle(graphics, qrX + qrSize + 20, qrY + (qrSize / 2), languageCode);

            // Draw bottom text (student name or "Visitor")
            string bottomText = badgeNumber == 1 ? studentName : "Visitor";
            var bottomFont = new System.Drawing.Font("Arial", 22, System.Drawing.FontStyle.Bold);
            var bottomTextSize = graphics.MeasureString(bottomText, bottomFont);
            int textY = qrY + qrSize + 20;
            var bottomTextLocation = new System.Drawing.PointF((badgeWidth - bottomTextSize.Width) / 2, textY);
            graphics.DrawString(bottomText, bottomFont, System.Drawing.Brushes.Black, bottomTextLocation);

            // Save to temporary file
            string tempPath = Path.Combine(Path.GetTempPath(), $"badge_{Guid.NewGuid():N}.png");
            badgeImage.Save(tempPath, System.Drawing.Imaging.ImageFormat.Png);

            return tempPath;
        }

        private void DrawLanguageCircle(Graphics graphics, int centerX, int centerY, string languageCode)
        {
            const int circleRadius = 50;
            
            // Draw circle border
            var pen = new System.Drawing.Pen(System.Drawing.Brushes.Black, 3);
            graphics.DrawEllipse(pen, centerX - circleRadius, centerY - circleRadius, circleRadius * 2, circleRadius * 2);

            // Fill circle background
            graphics.FillEllipse(System.Drawing.Brushes.White, centerX - circleRadius + 1, centerY - circleRadius + 1, (circleRadius * 2) - 2, (circleRadius * 2) - 2);

            // Draw language code text
            var languageFont = new System.Drawing.Font("Arial", 24, System.Drawing.FontStyle.Bold);
            var codeSize = graphics.MeasureString(languageCode, languageFont);
            var codeLocation = new System.Drawing.PointF(centerX - codeSize.Width / 2, centerY - codeSize.Height / 2);
            graphics.DrawString(languageCode, languageFont, System.Drawing.Brushes.Black, codeLocation);
        }

        private SKBitmap GenerateQRCode(string qrData)
        {
            var barcodeWriter = new BarcodeWriter<SKBitmap>
            {
                Format = BarcodeFormat.QR_CODE,
                Options = new EncodingOptions
                {
                    Width = 280,
                    Height = 280,
                    Margin = 0
                },
                Renderer = new SKBitmapRenderer()
            };

            return barcodeWriter.Write(qrData);
        }
#endif
    }
}
