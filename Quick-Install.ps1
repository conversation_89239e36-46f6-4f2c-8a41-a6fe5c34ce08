# Quick Install Script for UnitedPrint
# Run this as Administrator

Write-Host "UnitedPrint Quick Install" -ForegroundColor Green
Write-Host "========================" -ForegroundColor Green

# Get the script directory
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path

try {
    # Install certificate
    Write-Host "Installing certificate..." -ForegroundColor Yellow
    $certPath = Join-Path $scriptDir "UnitedPrint.cer"
    Import-Certificate -FilePath $certPath -CertStoreLocation Cert:\LocalMachine\Root -Verbose
    Import-Certificate -FilePath $certPath -CertStoreLocation Cert:\LocalMachine\TrustedPeople -Verbose
    
    Write-Host "Certificate installed successfully!" -ForegroundColor Green
    
    # Install app
    Write-Host "Installing UnitedPrint..." -ForegroundColor Yellow
    $appPath = Join-Path $scriptDir "unitedprint_1.0.9.0_x64.msix"
    Add-AppxPackage -Path $appPath -Verbose
    
    Write-Host "UnitedPrint installed successfully!" -ForegroundColor Green
    Write-Host "You can find it in your Start Menu." -ForegroundColor Cyan
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Manual steps:" -ForegroundColor Yellow
    Write-Host "1. Right-click UnitedPrint.cer -> Install Certificate -> Local Machine -> Trusted Root" -ForegroundColor White
    Write-Host "2. Then try installing the .msix file again" -ForegroundColor White
}

Read-Host "Press Enter to exit"
