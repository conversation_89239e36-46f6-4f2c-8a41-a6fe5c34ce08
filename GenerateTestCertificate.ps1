# Set the certificate parameters
$certName = "unitedprint_TestCertificate"
$certPassword = "P@ssw0rd123"  # This is a test password - change it in production!
$certPath = Join-Path $PSScriptRoot "$certName.pfx"

# Create parameters for New-SelfSignedCertificate
$params = @{
    Type = 'Custom'
    Subject = 'CN=UnitedPrint Test Certificate'
    KeyUsage = 'DigitalSignature'
    FriendlyName = 'UnitedPrint Test Certificate'
    CertStoreLocation = 'Cert:\ C:\Users\<USER>\Desktop\united2026\unitedprint\Certs\'
    TextExtension = @('*********={text}*******.*******.3', '*********={text}')
    KeySpec = 'Signature'
    KeyExportPolicy = 'Exportable'
    HashAlgorithm = 'sha256'
    KeyLength = 2048
    KeyAlgorithm = 'RSA'
    KeyUsageProperty = 'Sign'
    KeyProtection = 'None'
    Provider = 'Microsoft Enhanced RSA and AES Cryptographic Provider'
}

try {
    # Create a self-signed certificate
    $cert = New-SelfSignedCertificate @params
    
    # Export the certificate to a PFX file
    $securePassword = ConvertTo-SecureString -String $certPassword -Force -AsPlainText
    Export-PfxCertificate -Cert $cert -FilePath $certPath -Password $securePassword | Out-Null
    
    # Get the certificate thumbprint
    $thumbprint = $cert.Thumbprint
    
    # Output the information
    Write-Host "Certificate generated successfully!" -ForegroundColor Green
    Write-Host "Certificate file: $certPath"
    Write-Host "Certificate password: $certPassword"
    Write-Host "Certificate thumbprint: $thumbprint"
    
    # Update the project file with the new thumbprint
    $projectFile = Join-Path $PSScriptRoot "unitedprint\unitedprint.csproj"
    if (Test-Path $projectFile) {
        $content = Get-Content $projectFile -Raw
        $newContent = $content -replace '(<PackageCertificateThumbprint>)[^<]*(</PackageCertificateThumbprint>)', "`${1}$thumbprint`${2}"
        Set-Content -Path $projectFile -Value $newContent -NoNewline
        Write-Host "Updated project file with new certificate thumbprint" -ForegroundColor Green
    }
    
    Write-Host "`nIMPORTANT: Save this password in a secure location!" -ForegroundColor Yellow
    Write-Host "Password: $certPassword" -ForegroundColor Yellow
}
catch {
    Write-Host "Error generating certificate: $_" -ForegroundColor Red
    exit 1
}
