{"runtimeTarget": {"name": ".NETCoreApp,Version=v10.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v10.0": {}, ".NETCoreApp,Version=v10.0/win-x64": {"unitedprint/1.0.0": {"dependencies": {"Microsoft.Extensions.Logging.Debug": "9.0.0", "Microsoft.Maui.Controls": "9.0.10", "Microsoft.Maui.Controls.Compatibility": "9.0.10", "Microsoft.Maui.Graphics.Skia": "9.0.10", "System.Drawing.Common": "9.0.0", "System.Net.Http": "4.3.4", "System.Text.Json": "9.0.0", "ZXing.Net": "0.16.9", "ZXing.Net.Bindings.SkiaSharp": "0.16.9", "Microsoft.Web.WebView2.Core.Projection": "1.0.2792.45", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "10.0.0-preview.1.25080.5", "runtimepack.Microsoft.Windows.SDK.NET.Ref": "10.0.19041.57"}, "runtime": {"unitedprint.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/10.0.0-preview.1.25080.5": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "15.0.0.0", "fileVersion": "15.0.25.8005"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.AppContext.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Buffers.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Collections.Immutable.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Collections.Specialized.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Collections.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "10.0.25.8005"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.ComponentModel.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "10.0.25.8005"}, "System.Console.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "10.0.25.8005"}, "System.Data.Common.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "10.0.25.8005"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "10.0.25.8005"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Formats.Asn1.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Formats.Tar.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Globalization.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "10.0.25.8005"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.IO.Compression.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.IO.FileSystem.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.IO.Pipelines.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.IO.Pipes.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.IO.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Linq.AsyncEnumerable.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Linq.Expressions.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Linq.Parallel.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Linq.Queryable.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Linq.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Memory.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Net.Http.Json.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Net.Http.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Net.HttpListener.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Net.Mail.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Net.NameResolution.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Net.Ping.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Net.Primitives.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Net.Quic.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Net.Requests.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Net.Security.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Net.Sockets.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Net.WebClient.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Net.WebProxy.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Net.WebSockets.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "10.0.25.8005"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "10.0.25.8005"}, "System.ObjectModel.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Private.CoreLib.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Private.Uri.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Private.Xml.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Reflection.Emit.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Reflection.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Resources.Reader.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Resources.Writer.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Runtime.Handles.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Runtime.InteropServices.JavaScript.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Runtime.Loader.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "10.0.25.8005"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "10.0.25.8005"}, "System.Runtime.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Security.AccessControl.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Security.Claims.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Security.Cryptography.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Security.Principal.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Security.SecureString.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "10.0.25.8005"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "10.0.25.8005"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "10.0.25.8005"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Text.Encoding.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Text.Json.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Threading.Channels.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Threading.Tasks.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Threading.Thread.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Threading.Timer.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Threading.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Transactions.Local.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "10.0.25.8005"}, "System.ValueTuple.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "10.0.25.8005"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "10.0.25.8005"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "10.0.25.8005"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "10.0.25.8005"}, "System.Xml.XDocument.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Xml.XPath.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.8005"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "10.0.25.8005"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "10.0.25.8005"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "10.0.25.8005"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "10.0.25.8005"}, "netstandard.dll": {"assemblyVersion": "*******", "fileVersion": "10.0.25.8005"}}, "native": {"Microsoft.DiaSymReader.Native.amd64.dll": {"fileVersion": "14.40.33810.0"}, "System.IO.Compression.Native.dll": {"fileVersion": "10.0.25.8005"}, "clretwrc.dll": {"fileVersion": "10.0.25.8005"}, "clrgc.dll": {"fileVersion": "10.0.25.8005"}, "clrgcexp.dll": {"fileVersion": "10.0.25.8005"}, "clrjit.dll": {"fileVersion": "10.0.25.8005"}, "coreclr.dll": {"fileVersion": "10.0.25.8005"}, "createdump.exe": {"fileVersion": "10.0.25.8005"}, "hostfxr.dll": {"fileVersion": "10.0.25.8005"}, "hostpolicy.dll": {"fileVersion": "10.0.25.8005"}, "mscordaccore.dll": {"fileVersion": "10.0.25.8005"}, "mscordaccore_amd64_amd64_10.0.25.8005.dll": {"fileVersion": "10.0.25.8005"}, "mscordbi.dll": {"fileVersion": "10.0.25.8005"}, "mscorrc.dll": {"fileVersion": "10.0.25.8005"}, "msquic.dll": {"fileVersion": "*******"}}}, "runtimepack.Microsoft.Windows.SDK.NET.Ref/10.0.19041.57": {"runtime": {"Microsoft.Windows.SDK.NET.dll": {"assemblyVersion": "10.0.19041.38", "fileVersion": "10.0.19041.55"}, "WinRT.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.48161"}}}, "Microsoft.Extensions.Configuration/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.DependencyInjection/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Logging/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Logging.Debug/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Options/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Primitives/9.0.0": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Graphics.Win2D/1.2.0": {"dependencies": {"Microsoft.WindowsAppSDK": "1.6.240923002"}, "runtime": {"lib/net6.0-windows10.0.19041.0/Microsoft.Graphics.Canvas.Interop.dll": {"assemblyVersion": "1.2.0.0", "fileVersion": "1.2.0.0"}}, "native": {"runtimes/win-x64/native/Microsoft.Graphics.Canvas.dll": {"fileVersion": "1.2.0.0"}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"runtime": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"assemblyVersion": "3.0.1.0", "fileVersion": "3.0.1.0"}}}, "Microsoft.Maui.Controls/9.0.10": {"dependencies": {"Microsoft.Maui.Controls.Build.Tasks": "9.0.10", "Microsoft.Maui.Controls.Core": "9.0.10", "Microsoft.Maui.Controls.Xaml": "9.0.10", "Microsoft.Maui.Resizetizer": "9.0.10"}}, "Microsoft.Maui.Controls.Build.Tasks/9.0.10": {"dependencies": {"Microsoft.Maui.Controls.Core": "9.0.10", "Microsoft.Maui.Controls.Xaml": "9.0.10"}}, "Microsoft.Maui.Controls.Compatibility/9.0.10": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Maui.Controls.Core": "9.0.10", "Microsoft.Maui.Controls.Xaml": "9.0.10"}, "runtime": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.Compatibility.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "9.0.1024.56203"}}}, "Microsoft.Maui.Controls.Core/9.0.10": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Maui.Core": "9.0.10"}, "runtime": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "9.0.1024.56203"}}, "resources": {"lib/net9.0-windows10.0.19041/ar/Microsoft.Maui.Controls.resources.dll": {"locale": "ar"}, "lib/net9.0-windows10.0.19041/ca/Microsoft.Maui.Controls.resources.dll": {"locale": "ca"}, "lib/net9.0-windows10.0.19041/cs/Microsoft.Maui.Controls.resources.dll": {"locale": "cs"}, "lib/net9.0-windows10.0.19041/da/Microsoft.Maui.Controls.resources.dll": {"locale": "da"}, "lib/net9.0-windows10.0.19041/de/Microsoft.Maui.Controls.resources.dll": {"locale": "de"}, "lib/net9.0-windows10.0.19041/el/Microsoft.Maui.Controls.resources.dll": {"locale": "el"}, "lib/net9.0-windows10.0.19041/es/Microsoft.Maui.Controls.resources.dll": {"locale": "es"}, "lib/net9.0-windows10.0.19041/fi/Microsoft.Maui.Controls.resources.dll": {"locale": "fi"}, "lib/net9.0-windows10.0.19041/fr/Microsoft.Maui.Controls.resources.dll": {"locale": "fr"}, "lib/net9.0-windows10.0.19041/he/Microsoft.Maui.Controls.resources.dll": {"locale": "he"}, "lib/net9.0-windows10.0.19041/hi/Microsoft.Maui.Controls.resources.dll": {"locale": "hi"}, "lib/net9.0-windows10.0.19041/hr/Microsoft.Maui.Controls.resources.dll": {"locale": "hr"}, "lib/net9.0-windows10.0.19041/hu/Microsoft.Maui.Controls.resources.dll": {"locale": "hu"}, "lib/net9.0-windows10.0.19041/id/Microsoft.Maui.Controls.resources.dll": {"locale": "id"}, "lib/net9.0-windows10.0.19041/it/Microsoft.Maui.Controls.resources.dll": {"locale": "it"}, "lib/net9.0-windows10.0.19041/ja/Microsoft.Maui.Controls.resources.dll": {"locale": "ja"}, "lib/net9.0-windows10.0.19041/ko/Microsoft.Maui.Controls.resources.dll": {"locale": "ko"}, "lib/net9.0-windows10.0.19041/ms/Microsoft.Maui.Controls.resources.dll": {"locale": "ms"}, "lib/net9.0-windows10.0.19041/nb/Microsoft.Maui.Controls.resources.dll": {"locale": "nb"}, "lib/net9.0-windows10.0.19041/nl/Microsoft.Maui.Controls.resources.dll": {"locale": "nl"}, "lib/net9.0-windows10.0.19041/pl/Microsoft.Maui.Controls.resources.dll": {"locale": "pl"}, "lib/net9.0-windows10.0.19041/pt-BR/Microsoft.Maui.Controls.resources.dll": {"locale": "pt-BR"}, "lib/net9.0-windows10.0.19041/pt/Microsoft.Maui.Controls.resources.dll": {"locale": "pt"}, "lib/net9.0-windows10.0.19041/ro/Microsoft.Maui.Controls.resources.dll": {"locale": "ro"}, "lib/net9.0-windows10.0.19041/ru/Microsoft.Maui.Controls.resources.dll": {"locale": "ru"}, "lib/net9.0-windows10.0.19041/sk/Microsoft.Maui.Controls.resources.dll": {"locale": "sk"}, "lib/net9.0-windows10.0.19041/sv/Microsoft.Maui.Controls.resources.dll": {"locale": "sv"}, "lib/net9.0-windows10.0.19041/th/Microsoft.Maui.Controls.resources.dll": {"locale": "th"}, "lib/net9.0-windows10.0.19041/tr/Microsoft.Maui.Controls.resources.dll": {"locale": "tr"}, "lib/net9.0-windows10.0.19041/uk/Microsoft.Maui.Controls.resources.dll": {"locale": "uk"}, "lib/net9.0-windows10.0.19041/vi/Microsoft.Maui.Controls.resources.dll": {"locale": "vi"}, "lib/net9.0-windows10.0.19041/zh-HK/Microsoft.Maui.Controls.resources.dll": {"locale": "zh-HK"}, "lib/net9.0-windows10.0.19041/zh-Hans/Microsoft.Maui.Controls.resources.dll": {"locale": "zh-Hans"}, "lib/net9.0-windows10.0.19041/zh-Hant/Microsoft.Maui.Controls.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Maui.Controls.Xaml/9.0.10": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Maui.Controls.Core": "9.0.10", "Microsoft.Maui.Core": "9.0.10"}, "runtime": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.Xaml.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "9.0.1024.56203"}}}, "Microsoft.Maui.Core/9.0.10": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Graphics.Win2D": "1.2.0", "Microsoft.Maui.Essentials": "9.0.10", "Microsoft.Maui.Graphics": "9.0.10", "Microsoft.Maui.Graphics.Win2D.WinUI.Desktop": "9.0.10", "Microsoft.Web.WebView2": "1.0.2792.45", "Microsoft.Windows.SDK.BuildTools": "10.0.22621.756", "Microsoft.WindowsAppSDK": "1.6.240923002"}, "runtime": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "9.0.1024.56203"}}}, "Microsoft.Maui.Essentials/9.0.10": {"dependencies": {"Microsoft.Maui.Graphics": "9.0.10"}, "runtime": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Essentials.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "9.0.1024.56203"}}}, "Microsoft.Maui.Graphics/9.0.10": {"dependencies": {"Microsoft.Graphics.Win2D": "1.2.0", "Microsoft.IO.RecyclableMemoryStream": "3.0.1"}, "runtime": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Graphics.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "9.0.1024.56203"}}}, "Microsoft.Maui.Graphics.Skia/9.0.10": {"dependencies": {"Microsoft.Maui.Graphics": "9.0.10", "SkiaSharp": "2.88.8", "SkiaSharp.Views.WinUI": "2.88.8"}, "runtime": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Graphics.Skia.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "9.0.1024.56203"}}}, "Microsoft.Maui.Graphics.Win2D.WinUI.Desktop/9.0.10": {"dependencies": {"Microsoft.Graphics.Win2D": "1.2.0", "Microsoft.Maui.Graphics": "9.0.10"}, "runtime": {"lib/net9.0-windows10.0.19041/Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "9.0.1024.56203"}}}, "Microsoft.Maui.Resizetizer/9.0.10": {}, "Microsoft.NETCore.Platforms/1.1.1": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.Web.WebView2/1.0.2792.45": {"native": {"runtimes/win-x64/native/WebView2Loader.dll": {"fileVersion": "1.0.2792.45"}}}, "Microsoft.Win32.SystemEvents/9.0.0": {"runtime": {"runtimes/win/lib/net9.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Windows.SDK.BuildTools/10.0.22621.756": {}, "Microsoft.WindowsAppSDK/1.6.240923002": {"dependencies": {"Microsoft.Web.WebView2": "1.0.2792.45", "Microsoft.Windows.SDK.BuildTools": "10.0.22621.756"}, "runtime": {"lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.26107.1011"}, "lib/net6.0-windows10.0.18362.0/Microsoft.WinUI.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.2409"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"assemblyVersion": "1.6.0.0", "fileVersion": "1.6.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"assemblyVersion": "1.6.0.0", "fileVersion": "1.6.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Projection.dll": {"assemblyVersion": "1.6.0.0", "fileVersion": "1.6.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"assemblyVersion": "1.6.0.0", "fileVersion": "1.6.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"assemblyVersion": "1.6.0.0", "fileVersion": "1.6.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"assemblyVersion": "1.6.0.0", "fileVersion": "1.6.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Management.Deployment.Projection.dll": {"assemblyVersion": "1.6.0.0", "fileVersion": "1.6.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.PushNotifications.Projection.dll": {"assemblyVersion": "1.6.0.0", "fileVersion": "1.6.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"assemblyVersion": "1.6.0.0", "fileVersion": "1.6.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Storage.Projection.dll": {"assemblyVersion": "1.6.0.0", "fileVersion": "1.6.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Power.Projection.dll": {"assemblyVersion": "1.6.0.0", "fileVersion": "1.6.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Projection.dll": {"assemblyVersion": "1.6.0.0", "fileVersion": "1.6.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Widgets.Projection.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}, "lib/net6.0-windows10.0.18362.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {"assemblyVersion": "1.6.0.0", "fileVersion": "1.6.0.0"}}, "native": {"runtimes/win-x64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"fileVersion": "1.6.0.0"}}}, "NETStandard.Library/1.6.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1"}}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.0"}}, "SkiaSharp/2.88.8": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.8", "SkiaSharp.NativeAssets.macOS": "2.88.8"}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"assemblyVersion": "2.88.0.0", "fileVersion": "2.88.8.0"}}}, "SkiaSharp.NativeAssets.macOS/2.88.8": {}, "SkiaSharp.NativeAssets.Win32/2.88.8": {"native": {"runtimes/win-x64/native/libSkiaSharp.dll": {"fileVersion": "0.0.0.0"}}}, "SkiaSharp.Views.WinUI/2.88.8": {"dependencies": {"Microsoft.WindowsAppSDK": "1.6.240923002", "SkiaSharp": "2.88.8"}, "runtime": {"lib/net6.0-windows10.0.19041.0/SkiaSharp.Views.Windows.dll": {"assemblyVersion": "2.88.0.0", "fileVersion": "2.88.8.0"}}}, "System.Drawing.Common/9.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "9.0.0"}, "runtime": {"lib/net9.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52901"}, "lib/net9.0/System.Private.Windows.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52901"}}}, "System.Net.Http/4.3.4": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0"}}, "System.Text.Json/9.0.0": {}, "ZXing.Net/0.16.9": {"runtime": {"lib/net7.0/zxing.dll": {"assemblyVersion": "0.16.9.0", "fileVersion": "0.16.9.0"}}}, "ZXing.Net.Bindings.SkiaSharp/0.16.9": {"dependencies": {"NETStandard.Library": "1.6.0", "SkiaSharp": "2.88.8", "ZXing.Net": "0.16.9"}, "runtime": {"lib/netstandard1.3/ZXing.SkiaSharp.dll": {"assemblyVersion": "0.16.9.0", "fileVersion": "0.16.9.0"}}}, "Microsoft.Web.WebView2.Core.Projection/1.0.2792.45": {"runtime": {"Microsoft.Web.WebView2.Core.Projection.dll": {"assemblyVersion": "1.0.2792.45", "fileVersion": "1.0.2792.45"}}}}}, "libraries": {"unitedprint/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/10.0.0-preview.1.25080.5": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.Windows.SDK.NET.Ref/10.0.19041.57": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YIMO9T3JL8MeEXgVozKt2v79hquo/EFtnY0vgxmLnUvk1Rei/halI7kOWZL2RBeV9FMGzgM9LZA8CVaNwFMaNA==", "path": "microsoft.extensions.configuration/9.0.0", "hashPath": "microsoft.extensions.configuration.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lqvd7W3FGKUO1+ZoUEMaZ5XDJeWvjpy2/M/ptCGz3tXLD4HWVaSzjufsAsjemasBEg+2SxXVtYVvGt5r2nKDlg==", "path": "microsoft.extensions.configuration.abstractions/9.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MCPrg7v3QgNMr0vX4vzRXvkNGgLg8vKWX0nKCWUxu2uPyMsaRgiRc1tHBnbTcfJMhMKj2slE/j2M9oGkd25DNw==", "path": "microsoft.extensions.dependencyinjection/9.0.0", "hashPath": "microsoft.extensions.dependencyinjection.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+6f2qv2a3dLwd5w6JanPIPs47CxRbnk+ZocMJUhv9NxP88VlOcJYZs9jY+MYSjxvady08bUZn6qgiNh7DadGgg==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-crj<PERSON>yORoug0kK7RSNJBTeSE6VX8IQgLf3nUpTB9m62bPXp/tzbnOsnbe8TXEG0AASNaKZddnpHKw7fET8E++Pg==", "path": "microsoft.extensions.logging/9.0.0", "hashPath": "microsoft.extensions.logging.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-g0UfujELzlLbHoVG8kPKVBaW470Ewi+jnptGS9KUi6jcb+k2StujtK3m26DFSGGwQ/+bVgZfsWqNzlP6YOejvw==", "path": "microsoft.extensions.logging.abstractions/9.0.0", "hashPath": "microsoft.extensions.logging.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4wGlHsrLhYjLw4sFkfRixu2w4DK7dv60OjbvgbLGhUJk0eUPxYHhnszZ/P18nnAkfrPryvtOJ3ZTVev0kpqM6A==", "path": "microsoft.extensions.logging.debug/9.0.0", "hashPath": "microsoft.extensions.logging.debug.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-y2146b3jrPI3Q0lokKXdKLpmXqakYbDIPDV6r3M8SqvSf45WwOTzkyfDpxnZXJsJQEpAsAqjUq5Pu8RCJMjubg==", "path": "microsoft.extensions.options/9.0.0", "hashPath": "microsoft.extensions.options.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N3qEBzmLMYiASUlKxxFIISP4AiwuPTHF5uCh+2CWSwwzAJiIYx0kBJsS30cp1nvhSySFAVi30jecD307jV+8Kg==", "path": "microsoft.extensions.primitives/9.0.0", "hashPath": "microsoft.extensions.primitives.9.0.0.nupkg.sha512"}, "Microsoft.Graphics.Win2D/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bAo8ObjCy/br0eW0nONRfVKehJu5aDe/KQekWeNXslwTOO2rhrIfWaVGepsXyVqmqwHoLJ31g1HsT7FLdBCoQ==", "path": "microsoft.graphics.win2d/1.2.0", "hashPath": "microsoft.graphics.win2d.1.2.0.nupkg.sha512"}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-s/s20YTVY9r9TPfTrN5g8zPF1YhwxyqO6PxUkrYTGI2B+OGPe9AdajWZrLhFqXIvqIW23fnUE4+ztrUWNU1+9g==", "path": "microsoft.io.recyclablememorystream/3.0.1", "hashPath": "microsoft.io.recyclablememorystream.3.0.1.nupkg.sha512"}, "Microsoft.Maui.Controls/9.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-3P2OU0vcTZqVXz4u2Pvux6P6IB/dvY9tBGBzYVYG/+GPa06nN1naNkGuOed4rdlspKqmj9TmFp4O0d6GIX0ieA==", "path": "microsoft.maui.controls/9.0.10", "hashPath": "microsoft.maui.controls.9.0.10.nupkg.sha512"}, "Microsoft.Maui.Controls.Build.Tasks/9.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-EQpfTIjNuY/pfTG4+TbyEJAW63SoU0GuwdjY1E2sZIJdTxNFTD36nClmZJpVNWf4ySol8rPu2kfOznbDNhv35Q==", "path": "microsoft.maui.controls.build.tasks/9.0.10", "hashPath": "microsoft.maui.controls.build.tasks.9.0.10.nupkg.sha512"}, "Microsoft.Maui.Controls.Compatibility/9.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-WYJC+EAmRZaNQPNz3ajBXEa5Cb5i6+1QMi1uq7OQlO7UwY/hweQfgDJrBu0XlyT3wh9adelufNcUsIr2xqFXaA==", "path": "microsoft.maui.controls.compatibility/9.0.10", "hashPath": "microsoft.maui.controls.compatibility.9.0.10.nupkg.sha512"}, "Microsoft.Maui.Controls.Core/9.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-YBliIXb3Z0bAWjrldkIV9ku2o+ciejaypvQQyzebvA16A0wNmIYJY7uGdygVwSmpcPL73ZwwMCBPD4IqD1ogrg==", "path": "microsoft.maui.controls.core/9.0.10", "hashPath": "microsoft.maui.controls.core.9.0.10.nupkg.sha512"}, "Microsoft.Maui.Controls.Xaml/9.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-5cgWqRdTAtzps/Qz2RB4XZ1x5usVHeSjFVHE8LV2WG1avEztSteGQGmvgDYjnJrGPnCNcp4S7Ivh6mxDJwHDQQ==", "path": "microsoft.maui.controls.xaml/9.0.10", "hashPath": "microsoft.maui.controls.xaml.9.0.10.nupkg.sha512"}, "Microsoft.Maui.Core/9.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-7hrCvNUiF5hPLB+IDcFJ9ncD1ciPNJkUO1NxFMoeDKEttZqt/j8+za/XaGxRgzZWF1QaIXaee+WI8jwx4/G5+g==", "path": "microsoft.maui.core/9.0.10", "hashPath": "microsoft.maui.core.9.0.10.nupkg.sha512"}, "Microsoft.Maui.Essentials/9.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-F7bpJf7YP8Yxc3+oF9JzVZzEGuw3Pn30ULYomIyFKaUJvw0ceKGhv/QnXrKpwfAY/xAXTN7p0MVsRrEf5BsS1A==", "path": "microsoft.maui.essentials/9.0.10", "hashPath": "microsoft.maui.essentials.9.0.10.nupkg.sha512"}, "Microsoft.Maui.Graphics/9.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-QeM0B5H7OTqgY42sryVObbtvi55rWJPqFbBUZ6l344eaz07KsdUKon6WrcyJsuybVcLQcPV5HpLevFfk+lV0Yw==", "path": "microsoft.maui.graphics/9.0.10", "hashPath": "microsoft.maui.graphics.9.0.10.nupkg.sha512"}, "Microsoft.Maui.Graphics.Skia/9.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-ZDPUNEjz7fGkg2+XOXGjmoRG12FEqTIO6xIVWgBks25Dhs6d4hYaP9LvnS1uzne+YhKSPUCgFP3jCWLcCSiz2g==", "path": "microsoft.maui.graphics.skia/9.0.10", "hashPath": "microsoft.maui.graphics.skia.9.0.10.nupkg.sha512"}, "Microsoft.Maui.Graphics.Win2D.WinUI.Desktop/9.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-u8afvuWnjY+vlQpk8xWVU79+++g1ONWJhC9ehMnSpr9GK827mqKqWdZZIXFFb9pRoo+KGgbZ5TNujjUO6pIPpg==", "path": "microsoft.maui.graphics.win2d.winui.desktop/9.0.10", "hashPath": "microsoft.maui.graphics.win2d.winui.desktop.9.0.10.nupkg.sha512"}, "Microsoft.Maui.Resizetizer/9.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-cQLA4jeFF8mAVmqK6eJLSN374WK9+plVXpzxyVYd3Kes4d+JHRmjGrCrBDEkWlOXmH6jpAcGLlMa2utuuJDwOA==", "path": "microsoft.maui.resizetizer/9.0.10", "hashPath": "microsoft.maui.resizetizer.9.0.10.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-TMBuzAHpTenGbGgk0SMTwyEkyijY/Eae4ZGsFNYJvAr/LDn1ku3Etp3FPxChmDp5HHF3kzJuoaa08N0xjqAJfQ==", "path": "microsoft.netcore.platforms/1.1.1", "hashPath": "microsoft.netcore.platforms.1.1.1.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.Web.WebView2/1.0.2792.45": {"type": "package", "serviceable": true, "sha512": "sha512-K<PERSON>lLJSq70OySfU8mdhWdh9iOyApazWsIb6CmSz+YTJ5MmwLcsCLMW0qemORo7Si3A7VhLDIH3jwpMhPxodfkuA==", "path": "microsoft.web.webview2/1.0.2792.45", "hashPath": "microsoft.web.webview2.1.0.2792.45.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-z8FfGIaoeALdD+KF44A2uP8PZIQQtDGiXsOLuN8nohbKhkyKt7zGaZb+fKiCxTuBqG22Q7myIAioSWaIcOOrOw==", "path": "microsoft.win32.systemevents/9.0.0", "hashPath": "microsoft.win32.systemevents.9.0.0.nupkg.sha512"}, "Microsoft.Windows.SDK.BuildTools/10.0.22621.756": {"type": "package", "serviceable": true, "sha512": "sha512-7ZL2sFSioYm1Ry067Kw1hg0SCcW5kuVezC2SwjGbcPE61Nn+gTbH86T73G3LcEOVj0S3IZzNuE/29gZvOLS7VA==", "path": "microsoft.windows.sdk.buildtools/10.0.22621.756", "hashPath": "microsoft.windows.sdk.buildtools.10.0.22621.756.nupkg.sha512"}, "Microsoft.WindowsAppSDK/1.6.240923002": {"type": "package", "serviceable": true, "sha512": "sha512-7PfOz2scXU+AAM/GYge+f6s7k3DVI+R1P8MNPZQr56GOPCGw+csvcg3S5KZg47z/o04kNvWH3GKtWT1ML9tpZw==", "path": "microsoft.windowsappsdk/1.6.240923002", "hashPath": "microsoft.windowsappsdk.1.6.240923002.nupkg.sha512"}, "NETStandard.Library/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-ypsCvIdCZ4IoYASJHt6tF2fMo7N30NLgV1EbmC+snO490OMl9FvVxmumw14rhReWU3j3g7BYudG6YCrchwHJlA==", "path": "netstandard.library/1.6.0", "hashPath": "netstandard.library.1.6.0.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "SkiaSharp/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-bRkp3uKp5ZI8gXYQT57uKwil1uobb2p8c69n7v5evlB/2JNcMAXVcw9DZAP5Ig3WSvgzGm2YSn27UVeOi05NlA==", "path": "skiasharp/2.88.8", "hashPath": "skiasharp.2.88.8.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-6Kn5TSkKlfyS6azWHF3Jk2sW5C4jCE5uSshM/5AbfFrR+5n6qM5XEnz9h4VaVl7LTxBvHvMkuPb/3bpbq0vxTw==", "path": "skiasharp.nativeassets.macos/2.88.8", "hashPath": "skiasharp.nativeassets.macos.2.88.8.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-O9QXoWEXA+6cweR4h3BOnwMz+pO9vL9mXdjLrpDd0w1QzCgWmLQBxa1VgySDITiH7nQndrDG1h6937zm9pLj1Q==", "path": "skiasharp.nativeassets.win32/2.88.8", "hashPath": "skiasharp.nativeassets.win32.2.88.8.nupkg.sha512"}, "SkiaSharp.Views.WinUI/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-Zg+EgdDeAn8E8kCdP7WpBkOvdUHLHzmekluXLg5d4FSP0jrBEBs0uWc+dvf3A+OKlewJJzBJQSR6+mUuZe9YFg==", "path": "skiasharp.views.winui/2.88.8", "hashPath": "skiasharp.views.winui.2.88.8.nupkg.sha512"}, "System.Drawing.Common/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uoozjI3+dlgKh2onFJcz8aNLh6TRCPlLSh8Dbuljc8CdvqXrxHOVysJlrHvlsOCqceqGBR1wrMPxlnzzhynktw==", "path": "system.drawing.common/9.0.0", "hashPath": "system.drawing.common.9.0.0.nupkg.sha512"}, "System.Net.Http/4.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-aOa2d51SEbmM+H+Csw7yJOuNZoHkrP2XnAurye5HWYgGVVU54YZDvsLUYRv6h18X3sPnjNCANmN7ZhIPiqMcjA==", "path": "system.net.http/4.3.4", "hashPath": "system.net.http.4.3.4.nupkg.sha512"}, "System.Text.Json/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-js7+qAu/9mQvnhA4EfGMZNEzXtJCDxgkgj8ohuxq/Qxv+R56G+ljefhiJHOxTNiw54q8vmABCWUwkMulNdlZ4A==", "path": "system.text.json/9.0.0", "hashPath": "system.text.json.9.0.0.nupkg.sha512"}, "ZXing.Net/0.16.9": {"type": "package", "serviceable": true, "sha512": "sha512-7WaVMHklpT3Ye2ragqRIwlFRsb6kOk63BOGADV0fan3ulVfGLUYkDi5yNUsZS/7FVNkWbtHAlDLmu4WnHGfqvQ==", "path": "zxing.net/0.16.9", "hashPath": "zxing.net.0.16.9.nupkg.sha512"}, "ZXing.Net.Bindings.SkiaSharp/0.16.9": {"type": "package", "serviceable": true, "sha512": "sha512-GzvELDyw0RLlGToNTC8h8D4ApQe4M5mfLSwfpoqbmMn99dlRw7jEiHGo83Ys1JSoMyzs6+rD7OKnkIkhAdkoAw==", "path": "zxing.net.bindings.skiasharp/0.16.9", "hashPath": "zxing.net.bindings.skiasharp.0.16.9.nupkg.sha512"}, "Microsoft.Web.WebView2.Core.Projection/1.0.2792.45": {"type": "reference", "serviceable": false, "sha512": ""}}, "runtimes": {"win-x64": ["win", "any", "base"], "win-x64-aot": ["win-aot", "win-x64", "win", "aot", "any", "base"], "win10-x64": ["win10", "win81-x64", "win81", "win8-x64", "win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win10-x64-aot": ["win10-aot", "win10-x64", "win10", "win81-x64-aot", "win81-aot", "win81-x64", "win81", "win8-x64-aot", "win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"], "win7-x64": ["win7", "win-x64", "win", "any", "base"], "win7-x64-aot": ["win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"], "win8-x64": ["win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win8-x64-aot": ["win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"], "win81-x64": ["win81", "win8-x64", "win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win81-x64-aot": ["win81-aot", "win81-x64", "win81", "win8-x64-aot", "win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"]}}