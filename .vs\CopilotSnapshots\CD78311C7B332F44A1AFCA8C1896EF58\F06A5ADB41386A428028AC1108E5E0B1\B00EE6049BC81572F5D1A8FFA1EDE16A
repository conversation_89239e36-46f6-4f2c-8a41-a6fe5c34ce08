﻿using Microsoft.Maui.Controls;
using System.Text.Json;

namespace unitedprint
{
    public partial class StudentSelectionPage : ContentPage
    {
        private List<JsonElement> _students;
        private string _phoneNumber;

        public StudentSelectionPage()
        {
            InitializeComponent();
        }

        public StudentSelectionPage(List<JsonElement> students, string phoneNumber) : this()
        {
            _students = students;
            _phoneNumber = phoneNumber;
            LoadStudents();
        }

        private void LoadStudents()
        {
            try
            {
                StudentsContainer.Children.Clear();

                for (int i = 0; i < _students.Count; i++)
                {
                    var student = _students[i];
                    var studentCard = CreateStudentCard(student, i);
                    StudentsContainer.Children.Add(studentCard);
                }
            }
            catch (Exception ex)
            {
                DisplayAlert("Error", $"Failed to load students: {ex.Message}", "OK");
            }
        }

        private Frame CreateStudentCard(JsonElement student, int index)
        {
            // Extract only student name
            string studentName = "Unknown Student";
            if (student.TryGetProperty("student_name", out var nameElement))
                studentName = nameElement.GetString() ?? "Unknown Student";

            // Create the card with "Who Are You?" design
            var frame = new Frame
            {
                BackgroundColor = Color.FromArgb("#F8F9FA"),
                BorderColor = Color.FromArgb("#E0E0E0"),
                CornerRadius = 15,
                Padding = new Thickness(20, 15),
                Margin = new Thickness(0, 8),
                HasShadow = true
            };

            // Create horizontal layout for name and button
            var horizontalLayout = new StackLayout
            {
                Orientation = StackOrientation.Horizontal,
                HorizontalOptions = LayoutOptions.FillAndExpand,
                VerticalOptions = LayoutOptions.Center,
                Spacing = 15
            };

            // Info icon and "Who Are You?" section
            var leftSection = new StackLayout
            {
                Orientation = StackOrientation.Horizontal,
                HorizontalOptions = LayoutOptions.StartAndExpand,
                VerticalOptions = LayoutOptions.Center,
                Spacing = 10
            };

            // Info icon (using text since we don't have image)
            var infoIcon = new Label
            {
                Text = "ℹ️",
                FontSize = 24,
                VerticalOptions = LayoutOptions.Center
            };

            // Name section
            var nameSection = new StackLayout
            {
                Spacing = 2,
                VerticalOptions = LayoutOptions.Center
            };

            var whoAreYouLabel = new Label
            {
                Text = "Who Are You?",
                FontSize = 14,
                TextColor = Color.FromArgb("#666666"),
                FontAttributes = FontAttributes.Bold
            };

            var nameLabel = new Label
            {
                Text = studentName,
                FontSize = 18,
                FontAttributes = FontAttributes.Bold,
                TextColor = Color.FromArgb("#333333")
            };

            nameSection.Children.Add(whoAreYouLabel);
            nameSection.Children.Add(nameLabel);

            leftSection.Children.Add(infoIcon);
            leftSection.Children.Add(nameSection);

            // "I am" button
            var iAmButton = new Button
            {
                Text = "I am",
                BackgroundColor = Color.FromArgb("#1976D2"),
                TextColor = Colors.White,
                FontSize = 16,
                FontAttributes = FontAttributes.Bold,
                CornerRadius = 8,
                WidthRequest = 80,
                HeightRequest = 40,
                HorizontalOptions = LayoutOptions.End,
                VerticalOptions = LayoutOptions.Center
            };

            // Add click handler to button
            iAmButton.Clicked += async (sender, e) =>
            {
                // Visual feedback
                iAmButton.BackgroundColor = Color.FromArgb("#1565C0");
                await Task.Delay(100);
                iAmButton.BackgroundColor = Color.FromArgb("#1976D2");

                // Handle selection
                OnStudentSelected(index);
            };

            horizontalLayout.Children.Add(leftSection);
            horizontalLayout.Children.Add(iAmButton);

            frame.Content = horizontalLayout;

            return frame;
        }

        private async void OnStudentSelected(int index)
        {
            try
            {
                if (index >= 0 && index < _students.Count)
                {
                    var selectedStudent = _students[index];
                    
                    // Navigate to results page with selected student
                    var resultsPage = new ResultsPage(selectedStudent, _phoneNumber);
                    await Shell.Current.Navigation.PushAsync(resultsPage);
                    
                    // Remove this page from navigation stack
                    Shell.Current.Navigation.RemovePage(this);
                }
            }
            catch (Exception ex)
            {
                await DisplayAlert("Error", $"Failed to select student: {ex.Message}", "OK");
            }
        }

        private async void OnCancelClicked(object sender, EventArgs e)
        {
            // Go back to main page
            await Shell.Current.Navigation.PopAsync();
        }
    }
}
