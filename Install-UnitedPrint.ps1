# UnitedPrint Installation Script
# This script installs the certificate and then the UnitedPrint application

Write-Host "UnitedPrint Installation Script" -ForegroundColor Green
Write-Host "===============================" -ForegroundColor Green

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "This script requires administrator privileges to install the certificate." -ForegroundColor Yellow
    Write-Host "Please run PowerShell as Administrator and try again." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Alternative: You can install manually by:" -ForegroundColor Cyan
    Write-Host "1. Right-click UnitedPrint.cer -> Install Certificate" -ForegroundColor Cyan
    Write-Host "2. Choose 'Local Machine' -> 'Trusted Root Certification Authorities'" -ForegroundColor Cyan
    Write-Host "3. Then run: Add-AppxPackage unitedprint_1.0.9.0_x64.msix" -ForegroundColor Cyan
    pause
    exit
}

try {
    # Install the certificate
    Write-Host "Installing certificate..." -ForegroundColor Yellow
    $certPath = Join-Path $PSScriptRoot "UnitedPrint.cer"
    
    if (Test-Path $certPath) {
        Import-Certificate -FilePath $certPath -CertStoreLocation Cert:\LocalMachine\TrustedPeople
        Import-Certificate -FilePath $certPath -CertStoreLocation Cert:\LocalMachine\Root
        Write-Host "Certificate installed successfully!" -ForegroundColor Green
    } else {
        Write-Host "Certificate file not found: $certPath" -ForegroundColor Red
        exit 1
    }

    # Install the application
    Write-Host "Installing UnitedPrint application..." -ForegroundColor Yellow
    $appPath = Join-Path $PSScriptRoot "unitedprint_1.0.9.0_x64.msix"
    
    if (Test-Path $appPath) {
        Add-AppxPackage -Path $appPath
        Write-Host "UnitedPrint installed successfully!" -ForegroundColor Green
        Write-Host ""
        Write-Host "You can now find UnitedPrint in your Start Menu." -ForegroundColor Cyan
    } else {
        Write-Host "Application file not found: $appPath" -ForegroundColor Red
        exit 1
    }

} catch {
    Write-Host "Installation failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Manual installation steps:" -ForegroundColor Cyan
    Write-Host "1. Right-click UnitedPrint.cer -> Install Certificate" -ForegroundColor Cyan
    Write-Host "2. Choose 'Local Machine' -> 'Trusted Root Certification Authorities'" -ForegroundColor Cyan
    Write-Host "3. Run: Add-AppxPackage unitedprint_1.0.9.0_x64.msix" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "Installation completed!" -ForegroundColor Green
pause
