﻿<?xml version="1.0" encoding="utf-8"?>
<Package xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10" xmlns:uap="http://schemas.microsoft.com/appx/manifest/uap/windows10" xmlns:mp="http://schemas.microsoft.com/appx/2014/phone/manifest" xmlns:rescap="http://schemas.microsoft.com/appx/manifest/foundation/windows10/restrictedcapabilities" IgnorableNamespaces="uap rescap build" xmlns:build="http://schemas.microsoft.com/developer/appx/2015/build">
  <!--
    THIS PACKAGE MANIFEST FILE IS GENERATED BY THE BUILD PROCESS.

    Changes to this file will be lost when it is regenerated. To correct errors in this file, edit the source .appxmanifest file.

    For more information on package manifest files, see http://go.microsoft.com/fwlink/?LinkID=241727
  -->
  <Identity Name="com.unitededucation.unitedprint" Publisher="CN=User Name" Version="1.0.9.0" ProcessorArchitecture="x64" />
  <mp:PhoneIdentity PhoneProductId="A74F2B87-B10B-434A-8E69-02AD065CAA04" PhonePublisherId="00000000-0000-0000-0000-000000000000" />
  <Properties>
    <DisplayName>UnitedPrint</DisplayName>
    <PublisherDisplayName>User Name</PublisherDisplayName>
    <Logo>appiconStoreLogo.png</Logo>
  </Properties>
  <Dependencies>
    <TargetDeviceFamily Name="Windows.Universal" MinVersion="10.0.17763.0" MaxVersionTested="10.0.19041.0" />
    <TargetDeviceFamily Name="Windows.Desktop" MinVersion="10.0.17763.0" MaxVersionTested="10.0.19041.0" />
  </Dependencies>
  <Resources>
    <Resource Language="EN-US" />
  </Resources>
  <Applications>
    <Application Id="App" Executable="unitedprint.exe" EntryPoint="Windows.FullTrustApplication">
      <uap:VisualElements DisplayName="UnitedPrint" Description="UnitedPrint" Square150x150Logo="appiconMediumTile.png" Square44x44Logo="appiconLogo.png" BackgroundColor="transparent">
        <uap:DefaultTile Square71x71Logo="appiconSmallTile.png" Wide310x150Logo="appiconWideTile.png" Square310x310Logo="appiconLargeTile.png" ShortName="UnitedPrint">
          <uap:ShowNameOnTiles>
            <uap:ShowOn Tile="square150x150Logo" />
            <uap:ShowOn Tile="wide310x150Logo" />
          </uap:ShowNameOnTiles>
        </uap:DefaultTile>
        <uap:SplashScreen Image="search_logoSplashScreen.png" />
      </uap:VisualElements>
    </Application>
  </Applications>
  <Capabilities>
    <rescap:Capability Name="runFullTrust" />
  </Capabilities>
  <build:Metadata>
    <build:Item Name="XamlCompiler.exe" Version="3.0.0.2409" />
    <build:Item Name="makepri.exe" Version="10.0.22621.755 (WinBuild.160101.0800)" />
  </build:Metadata>
</Package>