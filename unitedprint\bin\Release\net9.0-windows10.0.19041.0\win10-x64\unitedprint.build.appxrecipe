﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Machine>YHYASOFT</Machine>
    <WindowsUser>yhyasoft</WindowsUser>
    <TargetPlatformIdentifier>Windows</TargetPlatformIdentifier>
    <TargetOsVersion>10.0</TargetOsVersion>
    <TargetOsDescription>Windows 10.0</TargetOsDescription>
    <SolutionConfiguration>Release|AnyCPU</SolutionConfiguration>
    <PackageArchitecture>x64</PackageArchitecture>
    <PackageIdentityName>com.companyname.unitedprint</PackageIdentityName>
    <PackageIdentityPublisher>CN=User Name</PackageIdentityPublisher>
    <IntermediateOutputPath>C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\</IntermediateOutputPath>
    <RemoteDeploymentType>CopyToDevice</RemoteDeploymentType>
    <PackageRegistrationPath></PackageRegistrationPath>
    <RemoveNonLayoutFiles>false</RemoveNonLayoutFiles>
    <DeployOptionalPackages>false</DeployOptionalPackages>
    <WindowsSdkPath>C:\Program Files %28x86%29\Windows Kits\10\</WindowsSdkPath>
    <LayoutDir>C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\bin\Release\net9.0-windows10.0.19041.0\win10-x64\AppX</LayoutDir>
  </PropertyGroup>
  <ItemGroup>
    <AppXManifest Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\bin\Release\net9.0-windows10.0.19041.0\win10-x64\AppxManifest.xml">
      <PackagePath>AppxManifest.xml</PackagePath>
      <ReRegisterAppIfChanged>true</ReRegisterAppIfChanged>
    </AppXManifest>
  </ItemGroup>
  <ItemGroup>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\bin\Release\net9.0-windows10.0.19041.0\win10-x64\resources.pri">
      <PackagePath>resources.pri</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\bin\Release\net9.0-windows10.0.19041.0\win10-x64\unitedprint.runtimeconfig.json">
      <PackagePath>unitedprint.runtimeconfig.json</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\apphost.exe">
      <PackagePath>unitedprint.exe</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\Resources\Raw\AboutAssets.txt">
      <PackagePath>AboutAssets.txt</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\sp\search_logoSplashScreen.scale-100.png">
      <PackagePath>search_logoSplashScreen.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\sp\search_logoSplashScreen.scale-125.png">
      <PackagePath>search_logoSplashScreen.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\sp\search_logoSplashScreen.scale-150.png">
      <PackagePath>search_logoSplashScreen.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\sp\search_logoSplashScreen.scale-200.png">
      <PackagePath>search_logoSplashScreen.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\sp\search_logoSplashScreen.scale-400.png">
      <PackagePath>search_logoSplashScreen.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\f\OpenSans-Regular.ttf">
      <PackagePath>OpenSans-Regular.ttf</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\f\OpenSans-Semibold.ttf">
      <PackagePath>OpenSans-Semibold.ttf</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appicon.ico">
      <PackagePath>appicon.ico</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLargeTile.scale-100.png">
      <PackagePath>appiconLargeTile.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLargeTile.scale-125.png">
      <PackagePath>appiconLargeTile.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLargeTile.scale-150.png">
      <PackagePath>appiconLargeTile.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLargeTile.scale-200.png">
      <PackagePath>appiconLargeTile.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLargeTile.scale-400.png">
      <PackagePath>appiconLargeTile.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-lightunplated_targetsize-16.png">
      <PackagePath>appiconLogo.altform-lightunplated_targetsize-16.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-lightunplated_targetsize-24.png">
      <PackagePath>appiconLogo.altform-lightunplated_targetsize-24.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-lightunplated_targetsize-256.png">
      <PackagePath>appiconLogo.altform-lightunplated_targetsize-256.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-lightunplated_targetsize-32.png">
      <PackagePath>appiconLogo.altform-lightunplated_targetsize-32.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-lightunplated_targetsize-48.png">
      <PackagePath>appiconLogo.altform-lightunplated_targetsize-48.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-unplated_targetsize-16.png">
      <PackagePath>appiconLogo.altform-unplated_targetsize-16.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-unplated_targetsize-24.png">
      <PackagePath>appiconLogo.altform-unplated_targetsize-24.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-unplated_targetsize-256.png">
      <PackagePath>appiconLogo.altform-unplated_targetsize-256.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-unplated_targetsize-32.png">
      <PackagePath>appiconLogo.altform-unplated_targetsize-32.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-unplated_targetsize-48.png">
      <PackagePath>appiconLogo.altform-unplated_targetsize-48.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.scale-100.png">
      <PackagePath>appiconLogo.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.scale-125.png">
      <PackagePath>appiconLogo.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.scale-150.png">
      <PackagePath>appiconLogo.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.scale-200.png">
      <PackagePath>appiconLogo.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.scale-400.png">
      <PackagePath>appiconLogo.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.targetsize-16.png">
      <PackagePath>appiconLogo.targetsize-16.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.targetsize-24.png">
      <PackagePath>appiconLogo.targetsize-24.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.targetsize-256.png">
      <PackagePath>appiconLogo.targetsize-256.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.targetsize-32.png">
      <PackagePath>appiconLogo.targetsize-32.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.targetsize-48.png">
      <PackagePath>appiconLogo.targetsize-48.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconMediumTile.scale-100.png">
      <PackagePath>appiconMediumTile.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconMediumTile.scale-125.png">
      <PackagePath>appiconMediumTile.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconMediumTile.scale-150.png">
      <PackagePath>appiconMediumTile.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconMediumTile.scale-200.png">
      <PackagePath>appiconMediumTile.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconMediumTile.scale-400.png">
      <PackagePath>appiconMediumTile.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconSmallTile.scale-100.png">
      <PackagePath>appiconSmallTile.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconSmallTile.scale-125.png">
      <PackagePath>appiconSmallTile.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconSmallTile.scale-150.png">
      <PackagePath>appiconSmallTile.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconSmallTile.scale-200.png">
      <PackagePath>appiconSmallTile.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconSmallTile.scale-400.png">
      <PackagePath>appiconSmallTile.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconStoreLogo.scale-100.png">
      <PackagePath>appiconStoreLogo.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconStoreLogo.scale-125.png">
      <PackagePath>appiconStoreLogo.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconStoreLogo.scale-150.png">
      <PackagePath>appiconStoreLogo.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconStoreLogo.scale-200.png">
      <PackagePath>appiconStoreLogo.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconStoreLogo.scale-400.png">
      <PackagePath>appiconStoreLogo.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconWideTile.scale-100.png">
      <PackagePath>appiconWideTile.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconWideTile.scale-125.png">
      <PackagePath>appiconWideTile.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconWideTile.scale-150.png">
      <PackagePath>appiconWideTile.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconWideTile.scale-200.png">
      <PackagePath>appiconWideTile.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconWideTile.scale-400.png">
      <PackagePath>appiconWideTile.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\dotnet_bot.scale-100.png">
      <PackagePath>dotnet_bot.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\dotnet_bot.scale-125.png">
      <PackagePath>dotnet_bot.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\dotnet_bot.scale-150.png">
      <PackagePath>dotnet_bot.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\dotnet_bot.scale-200.png">
      <PackagePath>dotnet_bot.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\dotnet_bot.scale-400.png">
      <PackagePath>dotnet_bot.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\edis_logo.scale-100.png">
      <PackagePath>edis_logo.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\edis_logo.scale-125.png">
      <PackagePath>edis_logo.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\edis_logo.scale-150.png">
      <PackagePath>edis_logo.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\edis_logo.scale-200.png">
      <PackagePath>edis_logo.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\edis_logo.scale-400.png">
      <PackagePath>edis_logo.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\edis_small_logo.scale-100.png">
      <PackagePath>edis_small_logo.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\edis_small_logo.scale-125.png">
      <PackagePath>edis_small_logo.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\edis_small_logo.scale-150.png">
      <PackagePath>edis_small_logo.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\edis_small_logo.scale-200.png">
      <PackagePath>edis_small_logo.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\edis_small_logo.scale-400.png">
      <PackagePath>edis_small_logo.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\eia_logo.scale-100.png">
      <PackagePath>eia_logo.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\eia_logo.scale-125.png">
      <PackagePath>eia_logo.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\eia_logo.scale-150.png">
      <PackagePath>eia_logo.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\eia_logo.scale-200.png">
      <PackagePath>eia_logo.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\eia_logo.scale-400.png">
      <PackagePath>eia_logo.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\eia_logo_copy.scale-100.png">
      <PackagePath>eia_logo_copy.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\image_five.scale-100.png">
      <PackagePath>image_five.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\image_fivehundred.scale-100.jpg">
      <PackagePath>image_fivehundred.scale-100.jpg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\image_four.scale-100.png">
      <PackagePath>image_four.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\image_one.scale-100.png">
      <PackagePath>image_one.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\logo_three.scale-100.png">
      <PackagePath>logo_three.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\logo_two.scale-100.png">
      <PackagePath>logo_two.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\search_logo.scale-100.png">
      <PackagePath>search_logo.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\search_logo.scale-125.png">
      <PackagePath>search_logo.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\search_logo.scale-150.png">
      <PackagePath>search_logo.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\search_logo.scale-200.png">
      <PackagePath>search_logo.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\search_logo.scale-400.png">
      <PackagePath>search_logo.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.2792.45\runtimes\win-x64\native\WebView2Loader.dll">
      <PackagePath>runtimes\win-x64\native\WebView2Loader.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\CoreMessagingXP.dll">
      <PackagePath>CoreMessagingXP.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\dcompi.dll">
      <PackagePath>dcompi.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\dwmcorei.dll">
      <PackagePath>dwmcorei.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\DwmSceneI.dll">
      <PackagePath>DwmSceneI.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\DWriteCore.dll">
      <PackagePath>DWriteCore.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\marshal.dll">
      <PackagePath>marshal.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.DirectManipulation.dll">
      <PackagePath>Microsoft.DirectManipulation.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.Graphics.Display.dll">
      <PackagePath>Microsoft.Graphics.Display.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.InputStateManager.dll">
      <PackagePath>Microsoft.InputStateManager.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.Internal.FrameworkUdk.dll">
      <PackagePath>Microsoft.Internal.FrameworkUdk.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.UI.Composition.OSSupport.dll">
      <PackagePath>Microsoft.UI.Composition.OSSupport.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.UI.dll">
      <PackagePath>Microsoft.UI.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.UI.Input.dll">
      <PackagePath>Microsoft.UI.Input.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.UI.Windowing.Core.dll">
      <PackagePath>Microsoft.UI.Windowing.Core.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.UI.Windowing.dll">
      <PackagePath>Microsoft.UI.Windowing.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.UI.Xaml.Controls.dll">
      <PackagePath>Microsoft.UI.Xaml.Controls.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.ui.xaml.dll">
      <PackagePath>Microsoft.ui.xaml.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.UI.Xaml.Internal.dll">
      <PackagePath>Microsoft.UI.Xaml.Internal.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.UI.Xaml.Phone.dll">
      <PackagePath>Microsoft.UI.Xaml.Phone.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.ui.xaml.resources.19h1.dll">
      <PackagePath>Microsoft.ui.xaml.resources.19h1.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.ui.xaml.resources.common.dll">
      <PackagePath>Microsoft.ui.xaml.resources.common.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.Windows.ApplicationModel.Resources.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.Resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.Windows.Widgets.dll">
      <PackagePath>Microsoft.Windows.Widgets.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.WindowsAppRuntime.dll">
      <PackagePath>Microsoft.WindowsAppRuntime.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.WindowsAppRuntime.Insights.Resource.dll">
      <PackagePath>Microsoft.WindowsAppRuntime.Insights.Resource.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\MRM.dll">
      <PackagePath>MRM.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\PushNotificationsLongRunningTask.ProxyStub.dll">
      <PackagePath>PushNotificationsLongRunningTask.ProxyStub.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\WindowsAppRuntime.DeploymentExtensions.OneCore.dll">
      <PackagePath>WindowsAppRuntime.DeploymentExtensions.OneCore.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\WindowsAppSdk.AppxDeploymentExtensions.Desktop-EventLog-Instrumentation.dll">
      <PackagePath>WindowsAppSdk.AppxDeploymentExtensions.Desktop-EventLog-Instrumentation.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\WindowsAppSdk.AppxDeploymentExtensions.Desktop.dll">
      <PackagePath>WindowsAppSdk.AppxDeploymentExtensions.Desktop.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\WinUIEdit.dll">
      <PackagePath>WinUIEdit.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\wuceffectsi.dll">
      <PackagePath>wuceffectsi.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\RestartAgent.exe">
      <PackagePath>RestartAgent.exe</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.UI.Xaml\Assets\map.html">
      <PackagePath>Microsoft.UI.Xaml\Assets\map.html</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\af-ZA\Microsoft.ui.xaml.dll.mui">
      <PackagePath>af-ZA\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\af-ZA\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>af-ZA\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\am-ET\Microsoft.ui.xaml.dll.mui">
      <PackagePath>am-ET\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\am-ET\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>am-ET\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ar-SA\Microsoft.ui.xaml.dll.mui">
      <PackagePath>ar-SA\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ar-SA\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>ar-SA\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\as-IN\Microsoft.ui.xaml.dll.mui">
      <PackagePath>as-IN\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\as-IN\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>as-IN\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\az-Latn-AZ\Microsoft.ui.xaml.dll.mui">
      <PackagePath>az-Latn-AZ\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\az-Latn-AZ\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>az-Latn-AZ\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\bg-BG\Microsoft.ui.xaml.dll.mui">
      <PackagePath>bg-BG\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\bg-BG\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>bg-BG\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\bn-IN\Microsoft.ui.xaml.dll.mui">
      <PackagePath>bn-IN\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\bn-IN\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>bn-IN\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\bs-Latn-BA\Microsoft.ui.xaml.dll.mui">
      <PackagePath>bs-Latn-BA\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\bs-Latn-BA\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>bs-Latn-BA\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ca-Es-VALENCIA\Microsoft.ui.xaml.dll.mui">
      <PackagePath>ca-Es-VALENCIA\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ca-Es-VALENCIA\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>ca-Es-VALENCIA\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ca-ES\Microsoft.ui.xaml.dll.mui">
      <PackagePath>ca-ES\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ca-ES\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>ca-ES\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\cs-CZ\Microsoft.ui.xaml.dll.mui">
      <PackagePath>cs-CZ\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\cs-CZ\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>cs-CZ\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\cy-GB\Microsoft.ui.xaml.dll.mui">
      <PackagePath>cy-GB\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\cy-GB\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>cy-GB\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\da-DK\Microsoft.ui.xaml.dll.mui">
      <PackagePath>da-DK\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\da-DK\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>da-DK\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\de-DE\Microsoft.ui.xaml.dll.mui">
      <PackagePath>de-DE\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\de-DE\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>de-DE\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\el-GR\Microsoft.ui.xaml.dll.mui">
      <PackagePath>el-GR\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\el-GR\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>el-GR\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\en-GB\Microsoft.ui.xaml.dll.mui">
      <PackagePath>en-GB\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\en-GB\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>en-GB\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\en-us\Microsoft.ui.xaml.dll.mui">
      <PackagePath>en-us\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\en-us\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>en-us\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\es-ES\Microsoft.ui.xaml.dll.mui">
      <PackagePath>es-ES\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\es-ES\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>es-ES\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\es-MX\Microsoft.ui.xaml.dll.mui">
      <PackagePath>es-MX\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\es-MX\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>es-MX\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\et-EE\Microsoft.ui.xaml.dll.mui">
      <PackagePath>et-EE\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\et-EE\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>et-EE\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\eu-ES\Microsoft.ui.xaml.dll.mui">
      <PackagePath>eu-ES\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\eu-ES\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>eu-ES\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\fa-IR\Microsoft.ui.xaml.dll.mui">
      <PackagePath>fa-IR\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\fa-IR\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>fa-IR\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\fi-FI\Microsoft.ui.xaml.dll.mui">
      <PackagePath>fi-FI\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\fi-FI\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>fi-FI\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\fil-PH\Microsoft.ui.xaml.dll.mui">
      <PackagePath>fil-PH\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\fil-PH\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>fil-PH\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\fr-CA\Microsoft.ui.xaml.dll.mui">
      <PackagePath>fr-CA\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\fr-CA\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>fr-CA\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\fr-FR\Microsoft.ui.xaml.dll.mui">
      <PackagePath>fr-FR\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\fr-FR\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>fr-FR\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ga-IE\Microsoft.ui.xaml.dll.mui">
      <PackagePath>ga-IE\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ga-IE\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>ga-IE\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\gd-gb\Microsoft.ui.xaml.dll.mui">
      <PackagePath>gd-gb\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\gd-gb\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>gd-gb\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\gl-ES\Microsoft.ui.xaml.dll.mui">
      <PackagePath>gl-ES\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\gl-ES\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>gl-ES\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\gu-IN\Microsoft.ui.xaml.dll.mui">
      <PackagePath>gu-IN\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\gu-IN\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>gu-IN\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\he-IL\Microsoft.ui.xaml.dll.mui">
      <PackagePath>he-IL\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\he-IL\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>he-IL\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\hi-IN\Microsoft.ui.xaml.dll.mui">
      <PackagePath>hi-IN\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\hi-IN\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>hi-IN\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\hr-HR\Microsoft.ui.xaml.dll.mui">
      <PackagePath>hr-HR\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\hr-HR\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>hr-HR\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\hu-HU\Microsoft.ui.xaml.dll.mui">
      <PackagePath>hu-HU\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\hu-HU\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>hu-HU\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\hy-AM\Microsoft.ui.xaml.dll.mui">
      <PackagePath>hy-AM\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\hy-AM\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>hy-AM\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\id-ID\Microsoft.ui.xaml.dll.mui">
      <PackagePath>id-ID\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\id-ID\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>id-ID\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\is-IS\Microsoft.ui.xaml.dll.mui">
      <PackagePath>is-IS\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\is-IS\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>is-IS\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\it-IT\Microsoft.ui.xaml.dll.mui">
      <PackagePath>it-IT\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\it-IT\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>it-IT\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ja-JP\Microsoft.ui.xaml.dll.mui">
      <PackagePath>ja-JP\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ja-JP\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>ja-JP\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ka-GE\Microsoft.ui.xaml.dll.mui">
      <PackagePath>ka-GE\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ka-GE\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>ka-GE\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\kk-KZ\Microsoft.ui.xaml.dll.mui">
      <PackagePath>kk-KZ\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\kk-KZ\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>kk-KZ\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\km-KH\Microsoft.ui.xaml.dll.mui">
      <PackagePath>km-KH\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\km-KH\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>km-KH\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\kn-IN\Microsoft.ui.xaml.dll.mui">
      <PackagePath>kn-IN\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\kn-IN\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>kn-IN\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ko-KR\Microsoft.ui.xaml.dll.mui">
      <PackagePath>ko-KR\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ko-KR\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>ko-KR\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\kok-IN\Microsoft.ui.xaml.dll.mui">
      <PackagePath>kok-IN\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\kok-IN\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>kok-IN\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\lb-LU\Microsoft.ui.xaml.dll.mui">
      <PackagePath>lb-LU\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\lb-LU\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>lb-LU\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\lo-LA\Microsoft.ui.xaml.dll.mui">
      <PackagePath>lo-LA\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\lo-LA\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>lo-LA\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\lt-LT\Microsoft.ui.xaml.dll.mui">
      <PackagePath>lt-LT\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\lt-LT\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>lt-LT\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\lv-LV\Microsoft.ui.xaml.dll.mui">
      <PackagePath>lv-LV\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\lv-LV\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>lv-LV\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\mi-NZ\Microsoft.ui.xaml.dll.mui">
      <PackagePath>mi-NZ\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\mi-NZ\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>mi-NZ\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\mk-MK\Microsoft.ui.xaml.dll.mui">
      <PackagePath>mk-MK\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\mk-MK\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>mk-MK\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ml-IN\Microsoft.ui.xaml.dll.mui">
      <PackagePath>ml-IN\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ml-IN\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>ml-IN\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\mr-IN\Microsoft.ui.xaml.dll.mui">
      <PackagePath>mr-IN\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\mr-IN\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>mr-IN\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ms-MY\Microsoft.ui.xaml.dll.mui">
      <PackagePath>ms-MY\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ms-MY\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>ms-MY\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\mt-MT\Microsoft.ui.xaml.dll.mui">
      <PackagePath>mt-MT\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\mt-MT\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>mt-MT\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\nb-NO\Microsoft.ui.xaml.dll.mui">
      <PackagePath>nb-NO\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\nb-NO\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>nb-NO\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ne-NP\Microsoft.ui.xaml.dll.mui">
      <PackagePath>ne-NP\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ne-NP\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>ne-NP\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\nl-NL\Microsoft.ui.xaml.dll.mui">
      <PackagePath>nl-NL\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\nl-NL\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>nl-NL\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\nn-NO\Microsoft.ui.xaml.dll.mui">
      <PackagePath>nn-NO\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\nn-NO\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>nn-NO\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\or-IN\Microsoft.ui.xaml.dll.mui">
      <PackagePath>or-IN\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\or-IN\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>or-IN\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\pa-IN\Microsoft.ui.xaml.dll.mui">
      <PackagePath>pa-IN\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\pa-IN\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>pa-IN\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\pl-PL\Microsoft.ui.xaml.dll.mui">
      <PackagePath>pl-PL\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\pl-PL\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>pl-PL\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\pt-BR\Microsoft.ui.xaml.dll.mui">
      <PackagePath>pt-BR\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\pt-BR\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>pt-BR\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\pt-PT\Microsoft.ui.xaml.dll.mui">
      <PackagePath>pt-PT\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\pt-PT\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>pt-PT\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\quz-PE\Microsoft.ui.xaml.dll.mui">
      <PackagePath>quz-PE\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\quz-PE\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>quz-PE\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ro-RO\Microsoft.ui.xaml.dll.mui">
      <PackagePath>ro-RO\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ro-RO\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>ro-RO\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ru-RU\Microsoft.ui.xaml.dll.mui">
      <PackagePath>ru-RU\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ru-RU\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>ru-RU\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\sk-SK\Microsoft.ui.xaml.dll.mui">
      <PackagePath>sk-SK\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\sk-SK\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>sk-SK\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\sl-SI\Microsoft.ui.xaml.dll.mui">
      <PackagePath>sl-SI\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\sl-SI\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>sl-SI\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\sq-AL\Microsoft.ui.xaml.dll.mui">
      <PackagePath>sq-AL\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\sq-AL\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>sq-AL\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\sr-Cyrl-BA\Microsoft.ui.xaml.dll.mui">
      <PackagePath>sr-Cyrl-BA\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\sr-Cyrl-BA\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>sr-Cyrl-BA\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\sr-Cyrl-RS\Microsoft.ui.xaml.dll.mui">
      <PackagePath>sr-Cyrl-RS\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\sr-Cyrl-RS\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>sr-Cyrl-RS\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\sr-Latn-RS\Microsoft.ui.xaml.dll.mui">
      <PackagePath>sr-Latn-RS\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\sr-Latn-RS\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>sr-Latn-RS\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\sv-SE\Microsoft.ui.xaml.dll.mui">
      <PackagePath>sv-SE\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\sv-SE\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>sv-SE\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ta-IN\Microsoft.ui.xaml.dll.mui">
      <PackagePath>ta-IN\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ta-IN\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>ta-IN\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\te-IN\Microsoft.ui.xaml.dll.mui">
      <PackagePath>te-IN\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\te-IN\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>te-IN\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\th-TH\Microsoft.ui.xaml.dll.mui">
      <PackagePath>th-TH\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\th-TH\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>th-TH\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\tr-TR\Microsoft.ui.xaml.dll.mui">
      <PackagePath>tr-TR\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\tr-TR\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>tr-TR\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\tt-RU\Microsoft.ui.xaml.dll.mui">
      <PackagePath>tt-RU\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\tt-RU\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>tt-RU\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ug-CN\Microsoft.ui.xaml.dll.mui">
      <PackagePath>ug-CN\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ug-CN\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>ug-CN\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\uk-UA\Microsoft.ui.xaml.dll.mui">
      <PackagePath>uk-UA\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\uk-UA\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>uk-UA\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ur-PK\Microsoft.ui.xaml.dll.mui">
      <PackagePath>ur-PK\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\ur-PK\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>ur-PK\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\uz-Latn-UZ\Microsoft.ui.xaml.dll.mui">
      <PackagePath>uz-Latn-UZ\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\uz-Latn-UZ\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>uz-Latn-UZ\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\vi-VN\Microsoft.ui.xaml.dll.mui">
      <PackagePath>vi-VN\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\vi-VN\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>vi-VN\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\zh-CN\Microsoft.ui.xaml.dll.mui">
      <PackagePath>zh-CN\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\zh-CN\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>zh-CN\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\zh-TW\Microsoft.ui.xaml.dll.mui">
      <PackagePath>zh-TW\Microsoft.ui.xaml.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\zh-TW\Microsoft.UI.Xaml.Phone.dll.mui">
      <PackagePath>zh-TW\Microsoft.UI.Xaml.Phone.dll.mui</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.UI.Xaml\Assets\NoiseAsset_256x256_PNG.png">
      <PackagePath>Microsoft.UI.Xaml\Assets\NoiseAsset_256x256_PNG.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\WindowsAppRuntime.png">
      <PackagePath>WindowsAppRuntime.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.Foundation.winmd">
      <PackagePath>Microsoft.Foundation.winmd</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.Graphics.winmd">
      <PackagePath>Microsoft.Graphics.winmd</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.UI.Text.winmd">
      <PackagePath>Microsoft.UI.Text.winmd</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.UI.winmd">
      <PackagePath>Microsoft.UI.winmd</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.UI.Xaml.winmd">
      <PackagePath>Microsoft.UI.Xaml.winmd</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.Windows.ApplicationModel.DynamicDependency.winmd">
      <PackagePath>Microsoft.Windows.ApplicationModel.DynamicDependency.winmd</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.Windows.ApplicationModel.Resources.winmd">
      <PackagePath>Microsoft.Windows.ApplicationModel.Resources.winmd</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.Windows.ApplicationModel.WindowsAppRuntime.winmd">
      <PackagePath>Microsoft.Windows.ApplicationModel.WindowsAppRuntime.winmd</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.Windows.AppLifecycle.winmd">
      <PackagePath>Microsoft.Windows.AppLifecycle.winmd</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.Windows.AppNotifications.Builder.winmd">
      <PackagePath>Microsoft.Windows.AppNotifications.Builder.winmd</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.Windows.AppNotifications.winmd">
      <PackagePath>Microsoft.Windows.AppNotifications.winmd</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.Windows.Globalization.winmd">
      <PackagePath>Microsoft.Windows.Globalization.winmd</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.Windows.Management.Deployment.winmd">
      <PackagePath>Microsoft.Windows.Management.Deployment.winmd</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.Windows.PushNotifications.winmd">
      <PackagePath>Microsoft.Windows.PushNotifications.winmd</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.Windows.Security.AccessControl.winmd">
      <PackagePath>Microsoft.Windows.Security.AccessControl.winmd</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.Windows.Storage.winmd">
      <PackagePath>Microsoft.Windows.Storage.winmd</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.Windows.System.Power.winmd">
      <PackagePath>Microsoft.Windows.System.Power.winmd</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.Windows.System.winmd">
      <PackagePath>Microsoft.Windows.System.winmd</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.Windows.Widgets.winmd">
      <PackagePath>Microsoft.Windows.Widgets.winmd</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\unitedprint.pdb">
      <PackagePath>unitedprint.pdb</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windows.sdk.net.ref\10.0.19041.57\lib\net8.0\Microsoft.Windows.SDK.NET.dll">
      <PackagePath>Microsoft.Windows.SDK.NET.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windows.sdk.net.ref\10.0.19041.57\lib\net8.0\WinRT.Runtime.dll">
      <PackagePath>WinRT.Runtime.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\ar\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ar\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\ca\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ca\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\cs\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>cs\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\da\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>da\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\de\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>de\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\el\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>el\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\es\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>es\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\fi\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>fi\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\fr\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>fr\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\he\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>he\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\hi\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>hi\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\hr\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>hr\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\hu\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>hu\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\id\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>id\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\it\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>it\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\ja\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ja\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\ko\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ko\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\ms\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ms\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\nb\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>nb\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\nl\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>nl\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\pl\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>pl\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\pt-BR\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>pt-BR\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\pt\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>pt\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\ro\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ro\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\ru\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ru\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\sk\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>sk\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\sv\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>sv\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\th\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>th\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\tr\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>tr\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\uk\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>uk\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\vi\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>vi\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\zh-HK\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>zh-HK\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\zh-Hans\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>zh-Hans\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\zh-Hant\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>zh-Hant\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.graphics.win2d\1.2.0\runtimes\win-x64\native\Microsoft.Graphics.Canvas.dll">
      <PackagePath>Microsoft.Graphics.Canvas.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.2792.45\runtimes\win-x64\native\WebView2Loader.dll">
      <PackagePath>WebView2Loader.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.6.240923002\runtimes\win-x64\native\Microsoft.WindowsAppRuntime.Bootstrap.dll">
      <PackagePath>Microsoft.WindowsAppRuntime.Bootstrap.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\skiasharp.nativeassets.win32\2.88.8\runtimes\win-x64\native\libSkiaSharp.dll">
      <PackagePath>libSkiaSharp.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.2792.45\runtimes\win-x64\native_uap\Microsoft.Web.WebView2.Core.dll">
      <PackagePath>Microsoft.Web.WebView2.Core.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.UI.pri">
      <PackagePath>Microsoft.UI.pri</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\MsixContent\Microsoft.UI.Xaml.Controls.pri">
      <PackagePath>Microsoft.UI.Xaml.Controls.pri</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Extensions.Configuration.dll">
      <PackagePath>Microsoft.Extensions.Configuration.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Extensions.Configuration.Abstractions.dll">
      <PackagePath>Microsoft.Extensions.Configuration.Abstractions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Extensions.DependencyInjection.dll">
      <PackagePath>Microsoft.Extensions.DependencyInjection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Extensions.DependencyInjection.Abstractions.dll">
      <PackagePath>Microsoft.Extensions.DependencyInjection.Abstractions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Extensions.Logging.dll">
      <PackagePath>Microsoft.Extensions.Logging.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Extensions.Logging.Abstractions.dll">
      <PackagePath>Microsoft.Extensions.Logging.Abstractions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Extensions.Logging.Debug.dll">
      <PackagePath>Microsoft.Extensions.Logging.Debug.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Extensions.Options.dll">
      <PackagePath>Microsoft.Extensions.Options.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Extensions.Primitives.dll">
      <PackagePath>Microsoft.Extensions.Primitives.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Graphics.Canvas.Interop.dll">
      <PackagePath>Microsoft.Graphics.Canvas.Interop.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.IO.RecyclableMemoryStream.dll">
      <PackagePath>Microsoft.IO.RecyclableMemoryStream.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Maui.Controls.Compatibility.dll">
      <PackagePath>Microsoft.Maui.Controls.Compatibility.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Maui.Controls.dll">
      <PackagePath>Microsoft.Maui.Controls.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Maui.Controls.Xaml.dll">
      <PackagePath>Microsoft.Maui.Controls.Xaml.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Maui.dll">
      <PackagePath>Microsoft.Maui.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Maui.Essentials.dll">
      <PackagePath>Microsoft.Maui.Essentials.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Maui.Graphics.dll">
      <PackagePath>Microsoft.Maui.Graphics.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Maui.Graphics.Skia.dll">
      <PackagePath>Microsoft.Maui.Graphics.Skia.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.dll">
      <PackagePath>Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Win32.SystemEvents.dll">
      <PackagePath>Microsoft.Win32.SystemEvents.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.InteractiveExperiences.Projection.dll">
      <PackagePath>Microsoft.InteractiveExperiences.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.WinUI.dll">
      <PackagePath>Microsoft.WinUI.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Windows.AppLifecycle.Projection.dll">
      <PackagePath>Microsoft.Windows.AppLifecycle.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Windows.AppNotifications.Builder.Projection.dll">
      <PackagePath>Microsoft.Windows.AppNotifications.Builder.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Windows.AppNotifications.Projection.dll">
      <PackagePath>Microsoft.Windows.AppNotifications.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Windows.ApplicationModel.Resources.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.Resources.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Windows.Management.Deployment.Projection.dll">
      <PackagePath>Microsoft.Windows.Management.Deployment.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Windows.PushNotifications.Projection.dll">
      <PackagePath>Microsoft.Windows.PushNotifications.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Windows.Security.AccessControl.Projection.dll">
      <PackagePath>Microsoft.Windows.Security.AccessControl.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Windows.Storage.Projection.dll">
      <PackagePath>Microsoft.Windows.Storage.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Windows.System.Power.Projection.dll">
      <PackagePath>Microsoft.Windows.System.Power.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Windows.System.Projection.dll">
      <PackagePath>Microsoft.Windows.System.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Windows.Widgets.Projection.dll">
      <PackagePath>Microsoft.Windows.Widgets.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.WindowsAppRuntime.Bootstrap.Net.dll">
      <PackagePath>Microsoft.WindowsAppRuntime.Bootstrap.Net.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\SkiaSharp.dll">
      <PackagePath>SkiaSharp.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\SkiaSharp.Views.Windows.dll">
      <PackagePath>SkiaSharp.Views.Windows.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\System.Drawing.Common.dll">
      <PackagePath>System.Drawing.Common.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\System.Private.Windows.Core.dll">
      <PackagePath>System.Private.Windows.Core.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\zxing.dll">
      <PackagePath>zxing.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\ZXing.SkiaSharp.dll">
      <PackagePath>ZXing.SkiaSharp.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\Microsoft.Web.WebView2.Core.Projection.dll">
      <PackagePath>Microsoft.Web.WebView2.Core.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\obj\Release\net9.0-windows10.0.19041.0\win10-x64\R2R\unitedprint.dll">
      <PackagePath>unitedprint.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Desktop\united2026\unitedprint\unitedprint\bin\Release\net9.0-windows10.0.19041.0\win10-x64\unitedprint.deps.json">
      <PackagePath>unitedprint.deps.json</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.compatibility\9.0.10\lib\net9.0-windows10.0.19041\Microsoft.Maui.Controls.Compatibility.pri">
      <PackagePath>Microsoft.Maui.Controls.Compatibility.pri</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.10\lib\net9.0-windows10.0.19041\Microsoft.Maui.Controls.pri">
      <PackagePath>Microsoft.Maui.Controls.pri</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.core\9.0.10\lib\net9.0-windows10.0.19041\Microsoft.Maui.pri">
      <PackagePath>Microsoft.Maui.pri</PackagePath>
    </AppxPackagedFile>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
</Project>
