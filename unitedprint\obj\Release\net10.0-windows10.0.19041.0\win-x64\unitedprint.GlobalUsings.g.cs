// <auto-generated/>
global using Microsoft.Extensions.DependencyInjection;
global using Microsoft.Maui;
global using Microsoft.Maui.Accessibility;
global using Microsoft.Maui.ApplicationModel;
global using Microsoft.Maui.ApplicationModel.Communication;
global using Microsoft.Maui.ApplicationModel.DataTransfer;
global using Microsoft.Maui.Authentication;
global using Microsoft.Maui.Controls;
global using Microsoft.Maui.Controls.Hosting;
global using Microsoft.Maui.Controls.Xaml;
global using Microsoft.Maui.Devices;
global using Microsoft.Maui.Devices.Sensors;
global using Microsoft.Maui.Dispatching;
global using Microsoft.Maui.Graphics;
global using Microsoft.Maui.Hosting;
global using Microsoft.Maui.Media;
global using Microsoft.Maui.Networking;
global using Microsoft.Maui.Storage;
global using System;
global using System.Collections.Generic;
global using System.IO;
global using System.Linq;
global using System.Net.Http;
global using System.Threading;
global using System.Threading.Tasks;
