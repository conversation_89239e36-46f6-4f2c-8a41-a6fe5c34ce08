# Create a self-signed certificate for code signing
$cert = New-SelfSignedCertificate -Type Custom -Subject "CN=UnitedPrint Test Certificate" -KeyUsage DigitalSignature -FriendlyName "UnitedPrint Test Certificate" -CertStoreLocation "Cert:\CurrentUser\My" -TextExtension @("*********={text}*******.*******.3", "*********={text}")

# Export the certificate to a file
$password = ConvertTo-SecureString -String "YourPassword123" -Force -AsPlainText
$certPath = "$PSScriptRoot\UnitedPrint_Test_Certificate.pfx"
Export-PfxCertificate -Cert $cert -FilePath $certPath -Password $password

# Import the certificate to the Trusted Root store
$rootStore = New-Object System.Security.Cryptography.X509Certificates.X509Store -ArgumentList "Root", "CurrentUser"
$rootStore.Open("ReadWrite")
$rootStore.Add($cert)
$rootStore.Close()

# Import the certificate to the Trusted Publisher store
$publisherStore = New-Object System.Security.Cryptography.X509Certificates.X509Store -ArgumentList "TrustedPublisher", "CurrentUser"
$publisherStore.Open("ReadWrite")
$publisherStore.Add($cert)
$publisherStore.Close()

Write-Host "Certificate has been created and installed successfully."
Write-Host "Certificate path: $certPath"
Write-Host "Certificate thumbprint: $($cert.Thumbprint)"
