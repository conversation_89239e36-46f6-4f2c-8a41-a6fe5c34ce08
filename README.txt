UnitedPrint Application - Installation Instructions
==================================================

AUTOMATIC INSTALLATION (Recommended):
Option A - Batch File (Easiest):
1. Right-click "Install-UnitedPrint.bat"
2. Select "Run as administrator"
3. Follow the on-screen instructions

Option B - PowerShell:
1. Right-click "Install-UnitedPrint.ps1"
2. Select "Run with PowerShell"
3. If prompted, choose "Yes" to run as administrator
4. Follow the on-screen instructions

CERTIFICATE PASSWORD: UnitedPrint2024
(Only needed if manual installation is required)

MANUAL INSTALLATION:
If the automatic installation doesn't work, follow these steps:

1. Install the Certificate:
   Option A (Easier - using .cer file):
   - Right-click "UnitedPrint.cer"
   - Select "Install Certificate"
   - Choose "Local Machine"
   - Select "Place all certificates in the following store"
   - Browse and select "Trusted Root Certification Authorities"
   - Click OK and Finish

   Option B (using .pfx file if .cer doesn't work):
   - Right-click "UnitedPrint.pfx"
   - Select "Install PFX"
   - Choose "Local Machine"
   - Enter password: UnitedPrint2024
   - Select "Place all certificates in the following store"
   - Browse and select "Trusted Root Certification Authorities"

2. Install the Application:
   - Open PowerShell as Administrator
   - Navigate to this folder
   - Run: Add-AppxPackage unitedprint_1.0.9.0_x64.msix

ALTERNATIVE INSTALLATION:
- Double-click "Add-AppDevPackage.ps1" and follow the prompts

TROUBLESHOOTING:
- If you get certificate errors, make sure you installed the certificate in "Trusted Root Certification Authorities"
- If PowerShell execution is blocked, run: Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
- For Windows 10/11, you may need to enable Developer Mode in Settings > Update & Security > For developers

SYSTEM REQUIREMENTS:
- Windows 10 version 1903 or later
- Windows 11 (any version)
- .NET 10 Runtime (will be installed automatically if missing)

FEATURES:
- Student search and registration
- QR code generation
- Badge printing with Zebra printers
- Event attendee registration via API

For support, contact your system administrator.
