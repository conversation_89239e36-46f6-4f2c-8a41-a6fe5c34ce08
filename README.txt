UnitedPrint Application - Installation Instructions
==================================================

AUTOMATIC INSTALLATION (Recommended):
1. Right-click "Install-UnitedPrint.ps1"
2. Select "Run with PowerShell"
3. If prompted, choose "Yes" to run as administrator
4. Follow the on-screen instructions

MANUAL INSTALLATION:
If the automatic installation doesn't work, follow these steps:

1. Install the Certificate:
   - Right-click "UnitedPrint.cer"
   - Select "Install Certificate"
   - Choose "Local Machine"
   - Select "Place all certificates in the following store"
   - <PERSON>rowse and select "Trusted Root Certification Authorities"
   - Click OK and Finish

2. Install the Application:
   - Open PowerShell as Administrator
   - Navigate to this folder
   - Run: Add-AppxPackage unitedprint_1.0.9.0_x64.msix

ALTERNATIVE INSTALLATION:
- Double-click "Add-AppDevPackage.ps1" and follow the prompts

TROUBLESHOOTING:
- If you get certificate errors, make sure you installed the certificate in "Trusted Root Certification Authorities"
- If PowerShell execution is blocked, run: Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
- For Windows 10/11, you may need to enable Developer Mode in Settings > Update & Security > For developers

SYSTEM REQUIREMENTS:
- Windows 10 version 1903 or later
- Windows 11 (any version)
- .NET 10 Runtime (will be installed automatically if missing)

FEATURES:
- Student search and registration
- QR code generation
- Badge printing with Zebra printers
- Event attendee registration via API

For support, contact your system administrator.
